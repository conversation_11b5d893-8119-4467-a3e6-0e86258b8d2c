<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net48</TargetFramework>
    <!-- <ImplicitUsings>enable</ImplicitUsings> --> <!-- Removed to support C# 7.3 -->
    <!-- <Nullable>enable</Nullable> --> <!-- Removed to support C# 7.3 -->
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Data" />
    <Reference Include="XrNet">
      <HintPath>..\\XrNet.dll</HintPath>
    </Reference>
    <Reference Include="ParamManager">
      <HintPath>..\ParamManager.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>