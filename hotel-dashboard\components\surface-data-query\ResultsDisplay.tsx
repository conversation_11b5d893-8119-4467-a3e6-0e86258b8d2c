'use client';

import React, { useState } from 'react';
import { SurfaceFile } from '@/types/surface-data';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, Eye } from 'lucide-react';
import PointCloudPreviewModal from './PointCloudPreviewModal';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface ResultsDisplayProps {
  results: SurfaceFile[];
  isLoading: boolean;
  selectedFiles: SurfaceFile[];
  setSelectedFiles: React.Dispatch<React.SetStateAction<SurfaceFile[]>>;
  onDownloadSelected: () => void;
}

export function ResultsDisplay({
  results,
  isLoading,
  selectedFiles,
  setSelectedFiles,
  onDownloadSelected,
}: ResultsDisplayProps) {
  const [selectedPreviewFile, setSelectedPreviewFile] = useState<SurfaceFile | null>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  const handleOpenPreview = (file: SurfaceFile) => {
    setSelectedPreviewFile(file);
    setIsPreviewOpen(true);
  };

  const handleClosePreview = () => {
    setIsPreviewOpen(false);
    setSelectedPreviewFile(null);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedFiles(results);
    } else {
      setSelectedFiles([]);
    }
  };

  const handleSelectFile = (file: SurfaceFile, checked: boolean) => {
    if (checked) {
      setSelectedFiles((prev) => [...prev, file]);
    } else {
      setSelectedFiles((prev) => prev.filter((f) => f.path !== file.path));
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTimestamp = (timestamp?: number): string => {
    if (!timestamp) return '-';
    return new Date(timestamp * 1000).toLocaleString(); // Assuming timestamp is in seconds
  };


  if (isLoading && results.length === 0) {
    return (
      <div className="flex justify-center items-center h-40">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="ml-2">正在加载搜索结果...</p>
      </div>
    );
  }

  if (!isLoading && results.length === 0) {
    return <div className="text-center p-4">没有搜索结果。</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">搜索结果 ({results.length} 条)</h2>
        <Button onClick={onDownloadSelected} disabled={selectedFiles.length === 0 || isLoading}>
          {isLoading && selectedFiles.length > 0 ? (
             <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                下载中...
             </>
          ) : (
            `下载选中 (${selectedFiles.length})`
          )}
        </Button>
      </div>
      <ScrollArea className="h-[400px] rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">
                <Checkbox
                  checked={results.length > 0 && selectedFiles.length === results.length}
                  onCheckedChange={handleSelectAll}
                  disabled={results.length === 0 || isLoading}
                />
              </TableHead>
              <TableHead className="w-[40%]">文件名</TableHead>
              <TableHead className="w-[20%]">大小</TableHead>
              <TableHead className="w-[25%]">修改时间</TableHead>
              <TableHead className="w-[15%] text-center">预览</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {results.map((file) => (
              <TableRow key={file.path}>
                <TableCell>
                  <Checkbox
                    checked={selectedFiles.some((f) => f.path === file.path)}
                    onCheckedChange={(checked) => handleSelectFile(file, !!checked)}
                    disabled={isLoading}
                  />
                </TableCell>
                <TableCell className="font-medium truncate max-w-xs" title={file.name}>{file.name}</TableCell>
                <TableCell>{formatFileSize(file.size)}</TableCell>
                <TableCell>{formatTimestamp(file.modifyTime)}</TableCell>
                <TableCell className="text-center">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleOpenPreview(file)}
                          aria-label={`预览 ${file.name}`}
                          disabled={isLoading}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>预览文件</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </ScrollArea>
      {selectedPreviewFile && (
        <PointCloudPreviewModal
          isOpen={isPreviewOpen}
          onClose={handleClosePreview}
          surfaceFile={selectedPreviewFile}
        />
      )}
    </div>
  );
}