'use client';

import React, { useState } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Loader2 } from 'lucide-react';

interface SearchBarProps {
  onSearch: (searchTerms: string, useRegex: boolean) => void;
  isLoading: boolean;
}

export function SearchBar({ onSearch, isLoading }: SearchBarProps) {
  const [searchTerms, setSearchTerms] = useState('');
  const [useRegex, setUseRegex] = useState(true); // 默认启用正则表达式

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(searchTerms, useRegex);
  };

  return (
    <form onSubmit={handleSubmit} className="p-4 border rounded-lg space-y-4 bg-card text-card-foreground">
      <div className="space-y-2">
        <Label htmlFor="searchTerms">搜索条件 (每行一个SN)</Label>
        <Textarea
          id="searchTerms"
          value={searchTerms}
          onChange={(e) => setSearchTerms(e.target.value)}
          placeholder="例如: GDDX2.*202506.*"
          rows={5} // 支持多行输入
          disabled={isLoading}
        />
      </div>
      <div className="flex items-center space-x-2 pt-2">
        <Switch
          id="useRegex"
          checked={useRegex}
          onCheckedChange={setUseRegex}
          disabled={isLoading}
        />
        <Label htmlFor="useRegex">使用正则表达式</Label>
      </div>
      <Button type="submit" disabled={isLoading} className="w-full md:w-auto">
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            搜索中...
          </>
        ) : (
          '搜索'
        )}
      </Button>
    </form>
  );
}