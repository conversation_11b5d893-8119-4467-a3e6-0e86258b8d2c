import { NextResponse } from 'next/server';
import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';

export async function POST(request: Request) {
  let filePath: string | null = null;

  try {
    const body = await request.json();
    const sqlQuery = body.query;

    if (!sqlQuery || typeof sqlQuery !== 'string') {
      return NextResponse.json({ error: 'Query is required and must be a string.' }, { status: 400 });
    }

    const wrapperAppDir = path.resolve(process.cwd(), '..', 'csharp-wrapper', 'bin', 'Debug', 'net48');
    const wrapperAppPath = path.join(wrapperAppDir, 'WrapperApp.exe');
    
    // Use fixed file path instead of extracting from stdout
    const tempDir = process.env.TEMP || process.env.TMP || 'C:\\Windows\\Temp';
    filePath = path.join(tempDir, 'db_query_result.json');
    
    // Delete existing file if it exists
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    
    // Escape quotes and other special characters for the command line
    const escapedQuery = sqlQuery.replace(/"/g, '\\"');
    const command = `"${wrapperAppPath}" "${escapedQuery}" --silent`;

    console.log(`Executing command: ${command} in ${wrapperAppDir}`);

    // Use PowerShell to redirect stderr to null
    const powershellCommand = `& "${wrapperAppPath}" "${escapedQuery}" --silent 2>$null`;

    const { stdout, stderr } = await new Promise<{stdout: string, stderr: string}>((resolve, reject) => {
      const child = spawn('powershell', ['-Command', powershellCommand], {
        cwd: wrapperAppDir,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdoutData = '';
      let stderrData = '';

      child.stdout.on('data', (data) => {
        stdoutData += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderrData += data.toString();
      });

      child.on('close', (code) => {
        if (code !== 0) {
          console.log(`Process stderr: ${stderrData}`);
          console.log(`Process stdout: ${stdoutData}`);
          reject(new Error(`Process exited with code ${code}. Stderr: ${stderrData}. Stdout: ${stdoutData}`));
        } else {
          resolve({ stdout: stdoutData, stderr: stderrData });
        }
      });

      child.on('error', (error) => {
        reject(error);
      });
    });

    console.log(`Process completed. Checking for file: ${filePath}`);

    // Check if the fixed file exists
    if (!fs.existsSync(filePath)) {
      console.error(`File not found: ${filePath}`);
      return NextResponse.json({ 
        error: 'Unable to find result file from C# wrapper.',
        details: `Expected file: ${filePath}`,
        stdout: stdout.substring(0, 500), // First 500 chars for debugging
        stderr: stderr.substring(0, 500)
      }, { status: 500 });
    }

    // Read the JSON file
    const jsonContent = fs.readFileSync(filePath, 'utf8');
    const data = JSON.parse(jsonContent);

    console.log(`Successfully read data from: ${filePath}`);

    return NextResponse.json({ 
      success: true, 
      data,
      filePath: filePath
    });

  } catch (error) {
    console.error('API Error:', error);
    
    // Clean up the file if it exists
    if (filePath && fs.existsSync(filePath)) {
      try {
        fs.unlinkSync(filePath);
      } catch (cleanupError) {
        console.error('Error cleaning up file:', cleanupError);
      }
    }

    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'An unknown error occurred.' 
    }, { status: 500 });
  }
}
