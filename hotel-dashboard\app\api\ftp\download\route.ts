import { NextRequest, NextResponse } from 'next/server';
import { downloadFileToBuffer } from '../../../../lib/ftpClient';
import { DownloadRequest } from '../../../../types/surface-data';
import archiver from 'archiver';
import { PassThrough } from 'stream';

export async function POST(request: NextRequest) {
  let requestBody: DownloadRequest;
  try {
    requestBody = await request.json();
  } catch { // Error variable is not used, so it can be removed.
    return NextResponse.json({ error: 'Invalid JSON in request body.' }, { status: 400 });
  }

  const { filePaths } = requestBody;

  if (!filePaths || !Array.isArray(filePaths) || filePaths.length === 0) {
    return NextResponse.json({ error: 'Missing or invalid filePaths in request body. Expecting a non-empty array of strings.' }, { status: 400 });
  }

  // Security: Validate filePaths to prevent path traversal or access to unauthorized files.
  // This is a basic example; a robust solution would involve more comprehensive validation
  // based on allowed base directories or specific file patterns.
  for (const filePath of filePaths) {
    if (filePath.includes('..')) {
      return NextResponse.json({ error: 'Invalid file path due to ".." sequence.' }, { status: 400 });
    }
    // Add more validation rules as needed
  }

  try {
    if (filePaths.length === 1) {
      // Single file download
      const filePath = filePaths[0];
      const fileName = filePath.split('/').pop() || 'downloaded-file';
      const buffer = await downloadFileToBuffer(filePath);
      
      return new NextResponse(buffer, {
        status: 200,
        headers: {
          'Content-Disposition': `attachment; filename="${fileName}"`,
          'Content-Type': 'application/octet-stream', // Or a more specific MIME type if known
          'Content-Length': buffer.length.toString(),
        },
      });

    } else {
      // Multiple files download as ZIP
      const archive = archiver('zip', {
        zlib: { level: 9 }, // Sets the compression level.
      });

      const passThrough = new PassThrough();
      archive.pipe(passThrough);

      // Asynchronously append files to the archive
      const downloadPromises = filePaths.map(async (filePath) => {
        try {
          const buffer = await downloadFileToBuffer(filePath);
          const fileName = filePath.split('/').pop() || 'unknown-file';
          archive.append(buffer, { name: fileName });
        } catch (downloadError) {
          console.error(`Failed to download and append file ${filePath} to zip:`, downloadError);
          // Optionally, you could inform the client about partial failures
          // For now, we'll let the archive finalize with successfully downloaded files
          archive.append(`Error downloading ${filePath}: ${(downloadError as Error).message}`, { name: `error-${filePath.split('/').pop() || 'unknown'}.txt` });
        }
      });

      // Wait for all files to be processed before finalizing the archive
      Promise.all(downloadPromises).then(() => {
        archive.finalize();
      }).catch(err => {
        // This catch is for errors in Promise.all itself, though individual errors are handled above.
        console.error("Error processing download promises for zip:", err);
        archive.finalize(); // Finalize even if some promises rejected, to send what we have.
      });
      
      const zipFileName = `surface_data_export_${Date.now()}.zip`;
      return new NextResponse(passThrough as any, { // Cast PassThrough to any for NextResponse
        status: 200,
        headers: {
          'Content-Disposition': `attachment; filename="${zipFileName}"`,
          'Content-Type': 'application/zip',
        },
      });
    }
  } catch (error: any) {
    console.error('FTP download API error:', error);
    return NextResponse.json({ error: 'Failed to download files from FTP server.', details: error.message }, { status: 500 });
  } finally {
    // Connection management considerations as in the search route.
    // closeFtpConnection(); // Potentially problematic.
  }
}