"use client";

import React, { useState, useRef } from 'react';
import Toolbar from './Toolbar';
import Canvas from './Canvas';
import { Shape, Alignment, ShapeType } from '@/types/drawing-board';

const DrawingBoard = () => {
  const [canvasSize, setCanvasSize] = useState({ width: 1920, height: 1080 });
  const [grid, setGrid] = useState({ rows: 9, cols: 17 });
  const [shapes, setShapes] = useState<Shape[]>([]);
  const [selectedColor, setSelectedColor] = useState('#000000');
  const [backgroundColor, setBackgroundColor] = useState('#FFFFFF');
  const [selectedShapeType, setSelectedShapeType] = useState<ShapeType>('circle');
  const [diameter, setDiameter] = useState(50);

  const addShape = (cell: { row: number; col: number }, alignment: Alignment, coordinates?: { x: number; y: number }) => {
    const newShape: Shape = {
      id: Date.now(),
      type: selectedShapeType,
      cell,
      color: selectedColor,
      diameter,
      alignment,
      coordinates,
    };
    setShapes([...shapes, newShape]);
  };

  const replaceColor = (oldColorHex: string, newColorHex: string) => {
    const oldColorLower = oldColorHex.toLowerCase();
    const newColorLower = newColorHex.toLowerCase();

    // Update background color if it matches
    if (backgroundColor.toLowerCase() === oldColorLower) {
      setBackgroundColor(newColorLower);
    }

    // Update shapes colors
    setShapes(prevShapes => 
      prevShapes.map(shape => 
        shape.color.toLowerCase() === oldColorLower
          ? { ...shape, color: newColorLower } 
          : shape
      )
    );
  };

  const deleteShape = (shapeId: number) => {
    setShapes(shapes.filter(shape => shape.id !== shapeId));
  };

  const resetCanvas = () => {
    setShapes([]);
    setBackgroundColor('#FFFFFF');
  };

  const exportCanvas = async (format: 'png' | 'jpeg') => {
    // Guard against SSR
    if (typeof document === 'undefined') {
      return;
    }
    // 不再需要二值化处理，因为我们已经使用像素级精确绘制

    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = canvasSize.width;
    tempCanvas.height = canvasSize.height;
    const ctx = tempCanvas.getContext('2d');

    if (!ctx) {
      console.error("Could not get temporary canvas context for exporting.");
      return;
    }

    // 1. Fill background - Use the current background color state
    ctx.fillStyle = backgroundColor;
    ctx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

    // 2. Re-implement drawing logic for shapes (without grid)
    const getShapeCenter = (shape: Shape): { cx: number, cy: number } => {
      const cellWidth = tempCanvas.width / grid.cols;
      const cellHeight = tempCanvas.height / grid.rows;
      const radius = shape.diameter / 2;
      const cellTopLeftX = shape.cell.col * cellWidth;
      const cellTopLeftY = shape.cell.row * cellHeight;
      let cx, cy;
      switch (shape.alignment) {
        case 'center':
          cx = cellTopLeftX + cellWidth / 2;
          cy = cellTopLeftY + cellHeight / 2;
          break;
        case 'topLeft':
          cx = cellTopLeftX + radius;
          cy = cellTopLeftY + radius;
          break;
        case 'coordinates':
          if (shape.coordinates) {
            cx = cellTopLeftX + shape.coordinates.x;
            cy = cellTopLeftY + shape.coordinates.y;
          } else {
            cx = cellTopLeftX + cellWidth / 2;
            cy = cellTopLeftY + cellHeight / 2;
          }
          break;
      }
      return { cx, cy };
    }

    const drawMtfPattern = (mainCtx: CanvasRenderingContext2D, x: number, y: number, size: number, color: string) => {
        const tempPatternCanvas = document.createElement('canvas');
        tempPatternCanvas.width = size;
        tempPatternCanvas.height = size;
        const tempCtx = tempPatternCanvas.getContext('2d');
        if (!tempCtx) return;

        tempCtx.fillStyle = '#FFFFFF';
        tempCtx.fillRect(0, 0, size, size);

        tempCtx.fillStyle = color;
        tempCtx.imageSmoothingEnabled = false;

        const center_x = size / 2;
        const center_y = size / 2;
        const lineWidth = 2;
        const gap = 2;
        const step = lineWidth + gap;

        tempCtx.fillRect(Math.round(0), Math.round(center_y - lineWidth / 2), Math.round(size), Math.round(lineWidth));
        tempCtx.fillRect(Math.round(center_x - lineWidth / 2), Math.round(0), Math.round(lineWidth), Math.round(size));

        for (let i = center_x - lineWidth / 2 - gap; i > 0; i -= step) {
            tempCtx.fillRect(Math.round(i - lineWidth), Math.round(0), Math.round(lineWidth), Math.round(size / 2 - lineWidth/2));
        }
        for (let i = center_y - lineWidth / 2 - gap; i > 0; i -= step) {
            tempCtx.fillRect(Math.round(center_x + lineWidth/2), Math.round(i - lineWidth), Math.round(size / 2 - lineWidth/2), Math.round(lineWidth));
        }
        for (let i = center_y + lineWidth / 2 + gap; i < size; i += step) {
            tempCtx.fillRect(Math.round(0), Math.round(i), Math.round(size / 2 - lineWidth/2), Math.round(lineWidth));
        }
        for (let i = center_x + lineWidth / 2 + gap; i < size; i += step) {
            tempCtx.fillRect(Math.round(i), Math.round(center_y + lineWidth/2), Math.round(lineWidth), Math.round(size / 2 - lineWidth/2));
        }

        mainCtx.imageSmoothingEnabled = false;
        mainCtx.drawImage(tempPatternCanvas, x, y);
    };

    for (const shape of shapes) {
        const { cx, cy } = getShapeCenter(shape);
        const radius = shape.diameter / 2;

        if (shape.type === 'circle') {
            // 使用像素级精确绘制来避免边缘颜色问题
            const tempCanvas = document.createElement('canvas');
            const tempSize = Math.ceil(shape.diameter) + 2; // 添加一些边距
            tempCanvas.width = tempSize;
            tempCanvas.height = tempSize;
            const tempCtx = tempCanvas.getContext('2d');

            if (tempCtx) {
              // 在临时画布上绘制圆形
              tempCtx.imageSmoothingEnabled = false;
              tempCtx.fillStyle = shape.color;

              const tempRadius = shape.diameter / 2;
              const tempCenter = tempSize / 2;

              // 使用像素级绘制，避免抗锯齿
              for (let x = 0; x < tempSize; x++) {
                for (let y = 0; y < tempSize; y++) {
                  const distance = Math.sqrt((x - tempCenter) ** 2 + (y - tempCenter) ** 2);
                  if (distance <= tempRadius) {
                    tempCtx.fillRect(x, y, 1, 1);
                  }
                }
              }

              // 将临时画布绘制到主画布上
              ctx.imageSmoothingEnabled = false;
              ctx.drawImage(
                tempCanvas,
                cx - shape.diameter / 2,
                cy - shape.diameter / 2,
                shape.diameter,
                shape.diameter
              );
            } else {
              // 如果临时画布创建失败，使用原始方法
              ctx.fillStyle = shape.color;
              ctx.beginPath();
              ctx.arc(cx, cy, radius, 0, 2 * Math.PI);
              ctx.fill();
            }
        } else if (shape.type === 'square') {
            drawMtfPattern(ctx, cx - radius, cy - radius, shape.diameter, shape.color);
        }
    }

    // 不再需要二值化后处理，因为我们已经使用像素级精确绘制

    // 3. Export the temporary canvas
    const dataUrl = tempCanvas.toDataURL(`image/${format}`);
    const link = document.createElement('a');
    link.href = dataUrl;
    link.download = `drawing-board-${Date.now()}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="flex flex-col md:flex-row h-full gap-4">
      <Toolbar
        canvasSize={canvasSize}
        setCanvasSize={setCanvasSize}
        setGrid={setGrid}
        grid={grid}
        selectedColor={selectedColor}
        setSelectedColor={setSelectedColor}
        selectedShapeType={selectedShapeType}
        setSelectedShapeType={setSelectedShapeType}
        diameter={diameter}
        setDiameter={setDiameter}
        replaceColor={replaceColor}
        resetCanvas={resetCanvas}
        exportCanvas={exportCanvas}
      />
      <div className="flex-grow overflow-auto">
        <Canvas
          width={canvasSize.width}
          height={canvasSize.height}
          grid={grid}
          shapes={shapes}
          addShape={addShape}
          deleteShape={deleteShape}
          backgroundColor={backgroundColor}
        />
      </div>
    </div>
  );
};

export default DrawingBoard;