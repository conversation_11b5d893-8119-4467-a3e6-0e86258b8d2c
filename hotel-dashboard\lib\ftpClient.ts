import { Client, FileInfo } from 'basic-ftp';
import { Readable, PassThrough } from 'stream'; // Added PassThrough
import { FtpFile } from '../types/surface-data';

const FTP_HOST = process.env.FTP_HOST || '';
const FTP_USER = process.env.FTP_USER || '';
const FTP_PASSWORD = process.env.FTP_PASSWORD || '';
const FTP_PORT = process.env.FTP_PORT ? parseInt(process.env.FTP_PORT, 10) : 21; // Default to 21 if not specified or invalid
const FTP_PATH_PREFIX = process.env.FTP_PATH_PREFIX || '/'; // Default to root if not specified

// Remove the global client instance for download operations to ensure isolation
// const client = new Client();

async function createNewClientAndConnect() {
  const newClient = new Client();
  // newClient.ftp.verbose = true; // Enable for debugging if needed
  try {
    await newClient.access({
      host: FTP_HOST,
      port: FTP_PORT,
      user: FTP_USER,
      password: FTP_PASSWORD,
      secure: false, // Use true for FTPS
    });
    console.log('FTP connected successfully for operation.');
    return newClient;
  } catch (err) {
    console.error('FTP connection error for operation:', err);
    newClient.close(); // Ensure client is closed on connection failure
    throw new Error('Failed to connect to FTP server for operation.');
  }
}

// listDirectory and searchFiles will also use createNewClientAndConnect.

function normalizePath(path: string): string {
  let normalized = path.replace(/\\/g, '/'); // Replace backslashes with forward slashes
  if (FTP_PATH_PREFIX && FTP_PATH_PREFIX !== '/') {
    normalized = `${FTP_PATH_PREFIX.replace(/\/$/, '')}/${normalized.replace(/^\//, '')}`;
  }
  return normalized.replace(/\/\//g, '/'); // Remove double slashes
}

export async function listDirectory(path: string = '.'): Promise<FtpFile[]> {
  const ftpClient = await createNewClientAndConnect(); // Use new client per operation
  const fullPath = normalizePath(path);
  try {
    const listing: FileInfo[] = await ftpClient.list(fullPath);
    return listing.map(item => ({
      type: item.isDirectory ? 'd' : item.isSymbolicLink ? 'l' : 'f',
      name: item.name,
      size: item.size,
      date: item.rawModifiedAt,
      path: `${fullPath}/${item.name}`.replace(/\/\//g, '/'),
    }));
  } catch (err) {
    console.error(`Error listing directory ${fullPath}:`, err);
    throw new Error(`Failed to list directory ${fullPath}.`);
  } finally {
    if (!ftpClient.closed) {
      ftpClient.close();
    }
  }
}

export async function searchFiles(
  basePath: string = '.',
  regexPattern: string,
  limit: number = 20
): Promise<FtpFile[]> {
  const ftpClient = await createNewClientAndConnect(); // Use new client per operation
  const fullBasePath = normalizePath(basePath);
  const results: FtpFile[] = [];
  const regex = new RegExp(regexPattern, 'i');

  async function recursiveSearch(currentPath: string) {
    if (results.length >= limit) {
      return;
    }
    let listing: FileInfo[];
    try {
      listing = await ftpClient.list(currentPath);
    } catch (err) {
      console.error(`Error listing directory ${currentPath} during search:`, err);
      return;
    }
    for (const item of listing) {
      if (results.length >= limit) break;
      const itemFullPath = `${currentPath}/${item.name}`.replace(/\/\//g, '/');
      if (item.isFile && regex.test(item.name)) {
        results.push({
          type: 'f',
          name: item.name,
          size: item.size,
          date: item.rawModifiedAt,
          path: itemFullPath,
        });
      } else if (item.isDirectory) {
        if (item.name !== '.' && item.name !== '..') {
           await recursiveSearch(itemFullPath);
        }
      }
    }
  }

  try {
    await recursiveSearch(fullBasePath);
  } catch (err) {
    console.error('Error during recursive search:', err);
    throw new Error('File search operation failed.');
  } finally {
    if (!ftpClient.closed) {
      ftpClient.close();
    }
  }
  return results.slice(0, limit);
}


export async function downloadFileToBuffer(filePath: string): Promise<Buffer> {
  const ftpClient = await createNewClientAndConnect(); // Each download gets a new client
  const fullPath = normalizePath(filePath);
  const passThroughStream = new PassThrough();
  const chunks: Buffer[] = [];

  try {
    // Start the download to the PassThrough stream
    // The downloadTo method will resolve once the file is fully written to the stream.
    const downloadPromise = ftpClient.downloadTo(passThroughStream, fullPath);

    const streamToBufferPromise = new Promise<Buffer>((resolve, reject) => {
      passThroughStream.on('data', (chunk: Buffer | string) => {
        chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk as string));
      });
      passThroughStream.on('end', () => {
        resolve(Buffer.concat(chunks));
      });
      passThroughStream.on('error', (err) => {
        console.error(`Error reading from PassThrough stream for file ${fullPath}:`, err);
        reject(new Error(`Failed to read stream for file ${fullPath}.`));
      });
    });

    // Wait for both the download to finish writing to the stream AND the stream to be fully read into chunks
    await downloadPromise; // Ensures downloadTo has completed
    // passThroughStream.end() might be called by downloadTo implicitly, or you might need to call it.
    // If downloadTo resolves when done, the 'end' event on passThroughStream should fire.

    return await streamToBufferPromise;

  } catch (err) {
    console.error(`Error initiating or during download for file ${fullPath}:`, err);
    // Ensure stream is destroyed on error to prevent leaks if ftpClient.downloadTo started it
    passThroughStream.destroy(err as Error);
    throw new Error(`Failed to download file ${fullPath}. Details: ${(err as Error).message}`);
  } finally {
    if (!ftpClient.closed) {
      ftpClient.close();
      console.log(`FTP connection closed for ${fullPath}`);
    }
  }
}

// Ensure client is closed when not needed, e.g., on application shutdown
// This is a simplified example; in a real app, manage client lifecycle carefully.
// closeFtpConnection might not be needed if each operation manages its own client.
// Or it could be a utility to close any lingering global/shared clients if those are still used elsewhere.
// For now, with per-operation clients, it's less critical for the download path.
export function closeFtpConnection() {
  // This function's utility is reduced if there's no global client.
  // If there was a global client `client`, it would be:
  // if (client && !client.closed) {
  //   client.close();
  //   console.log('Global FTP connection closed.');
  // }
  console.log('closeFtpConnection called - ensure all per-operation clients are closed in their respective functions.');
}

// Example of a more robust connection management might involve a pool or explicit open/close per operation group.
// For Next.js API routes, you might connect at the start of a request and close at the end,
// or use a shared client instance with careful error handling for disconnections.