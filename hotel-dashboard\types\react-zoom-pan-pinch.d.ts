declare module 'react-zoom-pan-pinch' {
  import * as React from 'react';

  export interface ReactZoomPanPinchRef {
    zoomIn: (step?: number) => void;
    zoomOut: (step?: number) => void;
    resetTransform: () => void;
    centerView: () => void;
    setTransform: (x: number, y: number, scale: number) => void;
  }
  
  export interface ReactZoomPanPinchContentRef extends ReactZoomPanPinchRef {
      instance: {
          transformState: {
              scale: number;
              positionX: number;
              positionY: number;
          };
      };
  }


  export const TransformWrapper: React.ForwardRefExoticComponent<
    React.PropsWithChildren<{
      ref?: React.Ref<ReactZoomPanPinchRef>;
      initialScale?: number;
      minScale?: number;
      maxScale?: number;
      centerOnInit?: boolean;
      limitToBounds?: boolean;
      doubleClick?: {
        mode?: 'zoomIn' | 'zoomOut' | 'reset';
      };
      children: React.ReactNode | ((utils: ReactZoomPanPinchContentRef) => React.ReactNode);
    }>
  >;

  export const TransformComponent: React.FC<React.PropsWithChildren<{
      wrapperStyle?: React.CSSProperties;
      contentStyle?: React.CSSProperties;
  }>>;
}