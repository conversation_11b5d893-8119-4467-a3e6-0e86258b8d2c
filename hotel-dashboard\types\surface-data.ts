// Placeholder for surface data type definitions
// Will be populated later

export interface FtpFile {
  type: 'f' | 'd' | 'l'; // f: file, d: directory, l: link
  name: string;
  size: number;
  date: string; // ISO string format
  path: string; // Full path on FTP server
}

export interface SurfaceFile {
  name: string;
  path: string; // Relative path from the search directory
  fullPath: string; // Full path on the FTP server
  size: number;
  modifyTime?: number; // Unix timestamp in seconds
  type: 'f' | 'd' | 'l'; // f: file, d: directory, l: link
}


export interface SearchResult {
  files: SurfaceFile[]; // Changed from FtpFile to SurfaceFile
  total: number;
  limit: number;
  offset: number;
}

export interface DownloadRequest {
  filePaths: string[];
}
// Point cloud preview related types
export interface Point3D {
  x: number;
  y: number;
  z: number;
}

export interface PointCloudBounds {
  minX: number;
  maxX: number;
  minY: number;
  maxY: number;
  minZ: number;
  maxZ: number;
}

export interface ParsedPointCloud {
  points: Point3D[];
  // Bounds might be calculated client-side, but good to have a type if ever needed from API
}

export interface PointCloudData extends ParsedPointCloud, PointCloudBounds {}


// API specific types for /api/surface-data/preview
export interface PreviewRequestBody {
  filePath: string;
}

// The preview API currently returns raw string data on success.
// If it were to return JSON, it might look like:
// export interface PreviewSuccessResponse {
//   rawData: string; // or ParsedPointCloud if processed server-side
//   message?: string;
// }

export interface PreviewErrorResponse {
  error: string;
  details?: any; // Optional field for more detailed error information
}