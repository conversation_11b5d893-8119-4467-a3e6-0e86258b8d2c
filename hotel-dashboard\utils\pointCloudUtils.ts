export interface PointXYZ {
  x: number;
  y: number;
  z: number;
}

/**
 * 根据归一化值从渐变色谱中获取颜色。
 * 移植自 Form1.cs 中的 GetColorFromGradient 方法。
 * @param normalizedValue - 归一化后的值 (0 到 1)。
 * @returns RGB颜色对象 { r: number, g: number, b: number }。
 */
export function getColorFromGradient(normalizedValue: number): { r: number; g: number; b: number } {
  if (normalizedValue < 0) normalizedValue = 0;
  if (normalizedValue > 1) normalizedValue = 1;

  if (normalizedValue < 0.25) {
    // 蓝色到青色
    const b = 255;
    const g = Math.round(255 * (normalizedValue * 4));
    return { r: 0, g, b };
  } else if (normalizedValue < 0.5) {
    // 青色到绿色
    const b = Math.round(255 * (1 - (normalizedValue - 0.25) * 4));
    const g = 255;
    return { r: 0, g, b };
  } else if (normalizedValue < 0.75) {
    // 绿色到黄色
    const r = Math.round(255 * ((normalizedValue - 0.5) * 4));
    const g = 255;
    return { r, g, b: 0 };
  } else {
    // 黄色到红色
    const g = Math.round(255 * (1 - (normalizedValue - 0.75) * 4));
    const r = 255;
    return { r, g, b: 0 };
  }
}

/**
 * 解析点云数据。
 * @param data - 原始点云数据字符串。
 * @returns 解析后的点云数据结构。
 */
export function parsePointCloudData(
  data: string,
  options?: { maxPoints?: number }
): PointXYZ[] {
  const points: PointXYZ[] = [];
  const lines = data.trim().split('\n');
  const totalPoints = lines.length;
  const maxPoints = options?.maxPoints;

  const shouldUseSampling = maxPoints && totalPoints > maxPoints;
  const step = shouldUseSampling ? Math.floor(totalPoints / maxPoints) : 1;

  for (let i = 0; i < totalPoints; i += step) {
    const line = lines[i];
    if (!line) continue;

    const parts = line.trim().split(/\s+/);
    if (parts.length === 3) {
      const x = parseFloat(parts[0]);
      const y = parseFloat(parts[1]);
      const z = parseFloat(parts[2]);
      if (!isNaN(x) && !isNaN(y) && !isNaN(z)) {
        points.push({ x, y, z });
      }
    }
  }
  return points;
}

/**
 * 计算点云数据的边界。
 * @param points - 点云数据点数组。
 * @returns 包含 minX, maxX, minY, maxY, minZ, maxZ 的边界对象。
 */
export function calculateBounds(points: PointXYZ[]): {
  minX: number;
  maxX: number;
  minY: number;
  maxY: number;
  minZ: number;
  maxZ: number;
} | null {
  if (!points || points.length === 0) {
    return null;
  }

  let minX = points[0].x;
  let maxX = points[0].x;
  let minY = points[0].y;
  let maxY = points[0].y;
  let minZ = points[0].z;
  let maxZ = points[0].z;

  for (const point of points) {
    if (point.x < minX) minX = point.x;
    if (point.x > maxX) maxX = point.x;
    if (point.y < minY) minY = point.y;
    if (point.y > maxY) maxY = point.y;
    if (point.z < minZ) minZ = point.z;
    if (point.z > maxZ) maxZ = point.z;
  }

  return { minX, maxX, minY, maxY, minZ, maxZ };
}