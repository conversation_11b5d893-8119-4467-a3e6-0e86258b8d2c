"use client";

import React, { useState, useEffect, useCallback, useRef } from 'react';
// LogChartView is removed as it will be handled by the parent page
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { exportElementAsImage } from '../../lib/exportUtils'; // 调整路径
import { useToast } from '@/components/ui/use-toast';
import { ProcessedBlock as WorkerProcessedBlock } from '@/workers/logParser.definitions';
import { Progress } from "@/components/ui/progress";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { LogChartView } from './LogChartView';

interface ChartDataItem {
  name: string;
  value: number;
  type: string;
  block_id: string;
}

interface ProcessedBlock extends WorkerProcessedBlock {
  data: ChartDataItem[];
}

interface LogDisplayAreaProps {
  dataChunks: ProcessedBlock[];
  onSelectionChange: (selectedBlocks: ProcessedBlock[]) => void;
  onStartExport: (exportIds: string[]) => void;
}

const LogDisplayArea: React.FC<LogDisplayAreaProps> = ({ dataChunks, onSelectionChange, onStartExport }) => {
  console.log('[LogDisplayArea] Rendering. ProcessedDataChunks count:', dataChunks.length);
  const [selectedBlockId, setSelectedBlockId] = useState<string>(''); // 单选显示
  const [exportBlockIds, setExportBlockIds] = useState<string[]>([]); // 多选导出
  const displayAreaRef = useRef<HTMLDivElement>(null);
  const [isExportingAll, setIsExportingAll] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const { toast } = useToast();

  const onSelectionChangeRef = React.useRef(onSelectionChange);
  useEffect(() => {
    onSelectionChangeRef.current = onSelectionChange;
  }, [onSelectionChange]);

  // 当有新的数据块时，自动选择第一个
useEffect(() => {
  console.log('[LogDisplayArea] useEffect for auto-selection triggered. processedDataChunks count:', dataChunks.length, 'current selectedBlockId:', selectedBlockId);
  if (dataChunks.length === 0) {
    if (selectedBlockId !== '') {
      console.log('[LogDisplayArea] No data chunks, clearing selectedBlockId.');
      setSelectedBlockId('');
    }
  } else {
    const currentSelectionIsValid = selectedBlockId &&
                                  selectedBlockId.trim() !== '' &&
                                  dataChunks.some(chunk => chunk.block_id === selectedBlockId);

    if (!currentSelectionIsValid) {
      console.log('[LogDisplayArea] Current selection is invalid or not set. Attempting to find first valid block.');
      const firstValidBlock = dataChunks.find(
        (chunk) => chunk.block_id && typeof chunk.block_id === 'string' && chunk.block_id.trim() !== ''
      );

      if (firstValidBlock) {
        console.log('[LogDisplayArea] Found first valid block. Setting selectedBlockId to:', firstValidBlock.block_id);
        setSelectedBlockId(firstValidBlock.block_id);
      } else {
        if (selectedBlockId !== '') {
          console.warn('[LogDisplayArea] No valid block_id found in any processed chunks. Clearing selectedBlockId.');
          setSelectedBlockId('');
        }
      }
    } else {
      console.log('[LogDisplayArea] Current selection is still valid:', selectedBlockId);
    }
  }
}, [dataChunks, selectedBlockId]); // Keep selectedBlockId in dependencies to re-evaluate if it changes externally or becomes invalid

  // 当显示选择改变时，通知父组件
useEffect(() => {
  if (selectedBlockId && dataChunks.length > 0) {
    const selected = dataChunks.filter(
      (chunk) => chunk.block_id === selectedBlockId
    );
    onSelectionChangeRef.current(selected); // 传递筛选出的块
  } else {
    onSelectionChangeRef.current([]); // 如果没有选中的ID或没有数据块，传递空数组
  }
}, [selectedBlockId, dataChunks]);

  const handleBlockSelectionChange = useCallback((blockId: string) => {
    console.log('[LogDisplayArea] handleBlockSelectionChange - START. blockId:', blockId);
    setSelectedBlockId(blockId);
    console.log('[LogDisplayArea] handleBlockSelectionChange - END.');
  }, []);

  const handleExportSelectionChange = useCallback((blockId: string) => {
    console.log('[LogDisplayArea] handleExportSelectionChange - START. blockId:', blockId);
    setExportBlockIds(prevSelected => {
      const newSelection = prevSelected.includes(blockId)
        ? prevSelected.filter(id => id !== blockId)
        : [...prevSelected, blockId];
      console.log('[LogDisplayArea] handleExportSelectionChange - New selection:', newSelection);
      return newSelection;
    });
  }, []);

  const selectAllForExport = useCallback(() => {
    console.log('[LogDisplayArea] selectAllForExport - START');
    const allIds = dataChunks.map(chunk => chunk.block_id);
    setExportBlockIds(allIds);
    console.log('[LogDisplayArea] selectAllForExport - END. Selected all IDs:', allIds);
  }, [dataChunks]);

  const deselectAllForExport = useCallback(() => {
    console.log('[LogDisplayArea] deselectAllForExport - START');
    setExportBlockIds([]);
    console.log('[LogDisplayArea] deselectAllForExport - END');
  }, []);

  const handleExportAllImages = async () => {
    console.log('handleExportAllImages called');
    console.log('displayAreaRef:', displayAreaRef.current);
    console.log('processedDataChunks:', dataChunks);
    console.log('exportBlockIds:', exportBlockIds);

    if (!displayAreaRef.current) {
      toast({ variant: 'destructive', title: '错误', description: '显示区域容器未找到。' });
      return;
    }
    if (!dataChunks || dataChunks.length === 0) {
      toast({ variant: 'destructive', title: '错误', description: '没有可导出的内容。' });
      return;
    }
    if (exportBlockIds.length === 0) {
      toast({ variant: 'destructive', title: '错误', description: '请至少选择一个数据块进行导出。' });
      return;
    }
    setIsExportingAll(true);
    setExportProgress(0);
    try {
      // 临时显示所有选中的图表
      const chartContainer = document.querySelector('.log-chart-container');
      if (!chartContainer) {
        throw new Error('找不到图表容器');
      }

      // 显示所有选中的图表
      const chartElements = chartContainer.querySelectorAll('[data-block-id]');
      chartElements.forEach((el) => {
        const blockId = el.getAttribute('data-block-id');
        if (blockId && exportBlockIds.includes(blockId)) {
          (el as HTMLElement).style.display = 'block';
        } else {
          (el as HTMLElement).style.display = 'none';
        }
      });

      // 等待图表渲染完成
      await new Promise(resolve => setTimeout(resolve, 1000));

      await exportElementAsImage(chartContainer as HTMLElement, `log-analysis-summary-${Date.now()}`, exportBlockIds, (progress) => {
        setExportProgress(progress);
      });

      // 恢复原来的显示状态
      chartElements.forEach((el) => {
        const blockId = el.getAttribute('data-block-id');
        if (blockId === selectedBlockId) {
          (el as HTMLElement).style.display = 'block';
        } else {
          (el as HTMLElement).style.display = 'none';
        }
      });
    } catch (error) {
      console.error("DisplayArea export failed in component:", error);
      toast({ variant: 'destructive', title: '导出失败', description: error instanceof Error ? error.message : '导出过程中发生错误。' });
    } finally {
      setIsExportingAll(false);
      setExportProgress(0);
    }
  };

  const hasContentToExport = dataChunks && dataChunks.length > 0;

  return (
    <Card className="h-[450px] flex flex-col">
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>选择数据块进行分析</CardTitle>
          <CardDescription>
            从解析的日志文件中选择一个数据块以在图表中显示。
          </CardDescription>
        </div>
        <Button onClick={handleExportAllImages} disabled={isExportingAll || !hasContentToExport || exportBlockIds.length === 0} size="sm">
          {isExportingAll ? (
            '导出中...'
          ) : (
            <>
              <Download className="mr-2 h-4 w-4" />
              导出选中图片
            </>
          )}
        </Button>
      </CardHeader>
      <CardContent className="flex-1 overflow-hidden p-4">
        <div ref={displayAreaRef} className="h-full flex flex-col">
          <div className="flex items-center space-x-2 mb-4">
            <Button onClick={selectAllForExport} size="sm" variant="outline">全选</Button>
            <Button onClick={deselectAllForExport} size="sm" variant="outline">取消全选</Button>
            <span className="text-sm text-muted-foreground ml-2">已选择导出: {exportBlockIds.length} 项</span>
            {isExportingAll && (
              <div className="flex-1 ml-4">
                <Progress value={exportProgress} className="h-1" />
              </div>
            )}
          </div>
          <div className="flex-1 overflow-y-auto min-h-0">
            <div className="space-y-2 p-2 border rounded-md">
              {dataChunks.map((chunk) => (
                <div key={chunk.block_id} className="flex items-center space-x-4">
                  <RadioGroup
                    value={selectedBlockId}
                    onValueChange={handleBlockSelectionChange}
                    className="flex items-center"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value={chunk.block_id} id={`display-${chunk.block_id}`} />
                      <Label htmlFor={`display-${chunk.block_id}`} className="cursor-pointer">
                        显示
                      </Label>
                    </div>
                  </RadioGroup>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`export-${chunk.block_id}`}
                      checked={exportBlockIds.includes(chunk.block_id)}
                      onChange={() => handleExportSelectionChange(chunk.block_id)}
                      className="h-4 w-4 rounded border-gray-300"
                    />
                    <Label htmlFor={`export-${chunk.block_id}`} className="cursor-pointer">
                      {`数据块 ${chunk.block_id} (胶厚: ${chunk.glue_thickness_values.length}, 准直: ${chunk.collimation_diff_values.length})`}
                    </Label>
                  </div>
                </div>
              ))}
            </div>
          </div>
          {(!dataChunks || dataChunks.length === 0) && (
            <p className="text-muted-foreground p-2">暂无数据块可供分析或导出。</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default LogDisplayArea;