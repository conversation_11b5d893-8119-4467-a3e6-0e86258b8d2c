// hotel-dashboard/workers/logParser.worker.ts
/// <reference lib="webworker" />

// Import types from the definitions file
import type {
  TimestampedValue,
  ValveOpenEvent,
  ProcessedBlock,
  // FormattedDataPoint, // Not directly used in worker logic, but ProcessedBlock uses it
  // FormattedBlockData // Not directly used in worker logic
} from './logParser.definitions';

// Check if running in a Web Worker context
// Condition:
// 1. `self` is undefined (definitely not a worker or similar context)
// 2. `postMessage` function doesn't exist on `self` (essential for workers)
// 3. `self` is strictly equal to `window` (rules out main browser thread where self === window, but allows ServiceWorkers where self exists but self !== window)
if (
    typeof DedicatedWorkerGlobalScope === "undefined" ||
    !(self instanceof DedicatedWorkerGlobalScope) ||
    typeof self.postMessage !== 'function'
) {
  console.warn('[Worker] logParser.worker.ts loaded in non-worker context (or main thread). Exports are available, but worker-specific code will not run.');
} else {
  // Proceed with worker logic only if 'self' is defined, has postMessage, and is not the main window object.
  const DEBUG = false; // Set to true to enable detailed logs

  // Regex for extracting timestamp from a log line
  const TIMESTAMP_REGEX = /^\w+\s+(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2},\d{3})/;

  function _extractTimestampFromLine(line: string): string | null {
      const match = TIMESTAMP_REGEX.exec(line);
      return match ? match[1] : null;
  }

  const GLUE_REGEX = /####### 胶厚值:([+-]?(?:\d+\.?\d*|\.\d+))/;
  const DIFF_REGEX = /####### 准直diff:([+-]?(?:\d+\.?\d*|\.\d+))/;

  function processRawBlock(blockId: string, blockContent: string): ProcessedBlock | null {
      if (DEBUG) console.log(`[Worker] Processing block ${blockId}. Content snippet: ${blockContent.substring(0, 100)}...`);
      if (!blockContent) return null;
      const blockLines = blockContent.split('\n');
      if (!blockLines.length) return null;

      let startTimeStr: string | null = null;
      let endTimeStr: string | null = null;

      if (blockLines.length > 0) {
          for (let i = 0; i < blockLines.length; i++) {
              const ts = _extractTimestampFromLine(blockLines[i]);
              if (ts) {
                  startTimeStr = ts;
                  break;
              }
          }
          for (let i = blockLines.length - 1; i >= 0; i--) {
              const ts = _extractTimestampFromLine(blockLines[i]);
              if (ts) {
                  endTimeStr = ts;
                  break;
              }
          }
          if (!endTimeStr && startTimeStr) {
              endTimeStr = startTimeStr;
          }
      }
      if (DEBUG) console.log(`[Worker] Block ${blockId}: startTime=${startTimeStr}, endTime=${endTimeStr}`);

      const valveOpenEventsList: ValveOpenEvent[] = [];
      const glueThicknessValuesList: TimestampedValue[] = [];
      const collimationDiffValuesList: TimestampedValue[] = [];

      blockLines.forEach((line, index) => {
          const currentLineTimestamp = _extractTimestampFromLine(line);

          if (line.includes("打开放气阀")) {
              if (currentLineTimestamp) {
                  valveOpenEventsList.push({ timestamp: currentLineTimestamp, line_content: line.trim() });
                  if (DEBUG) console.log(`[Worker] Block ${blockId}, Line ${index + 1}: Found '打开放气阀' at ${currentLineTimestamp}`);
              }
          }

          const glueMatch = GLUE_REGEX.exec(line);
          if (glueMatch) {
              try {
                  const valueStr = glueMatch[1];
                  const valueFloat = parseFloat(valueStr);
                  const timestampForValue = currentLineTimestamp || startTimeStr;
                  if (timestampForValue) {
                      glueThicknessValuesList.push({ timestamp: timestampForValue, value: valueFloat });
                      if (DEBUG) console.log(`[Worker] Block ${blockId}, Line ${index + 1}: Found Glue: ${valueFloat} at ${timestampForValue}`);
                  } else {
                      if (DEBUG) console.warn(`[Worker] Skipping glue value due to missing timestamp: ${valueFloat} in line "${line.trim()}"`);
                  }
              } catch (e) {
                  if (DEBUG) console.warn(`[Worker] Could not parse glue value from "${glueMatch[1]}" in line "${line.trim()}":`, e);
              }
          }

          const diffMatch = DIFF_REGEX.exec(line);
          if (diffMatch) {
              try {
                  const valueStr = diffMatch[1];
                  const valueFloat = parseFloat(valueStr);
                  const timestampForValue = currentLineTimestamp || startTimeStr;
                  if (timestampForValue) {
                      collimationDiffValuesList.push({ timestamp: timestampForValue, value: valueFloat });
                      if (DEBUG) console.log(`[Worker] Block ${blockId}, Line ${index + 1}: Found Diff: ${valueFloat} at ${timestampForValue}`);
                  } else {
                      if (DEBUG) console.warn(`[Worker] Skipping diff value due to missing timestamp: ${valueFloat} in line "${line.trim()}"`);
                  }
              } catch (e) {
                  if (DEBUG) console.warn(`[Worker] Could not parse diff value from "${diffMatch[1]}" in line "${line.trim()}":`, e);
              }
          }
      });
      if (DEBUG) {
          console.log(`[Worker] Block ${blockId} processing finished. Glue values found: ${glueThicknessValuesList.length}, Diff values found: ${collimationDiffValuesList.length}`);
      }

      return {
          block_id: blockId,
          start_time: startTimeStr,
          end_time: endTimeStr,
          lines_count: blockLines.length,
          valve_open_events: valveOpenEventsList,
          glue_thickness_values: glueThicknessValuesList,
          collimation_diff_values: collimationDiffValuesList,
      };
  }

  const BLOCK_REGEX = /打开真空泵(?:(?!打开真空泵)[\s\S])*?insert into g_support/g;

  function parseLogContent(logContent: string): ProcessedBlock[] {
      if (DEBUG) console.log(`[Worker] Starting parseLogContent. Content length: ${logContent ? logContent.length : 0}`);
      if (!logContent) return [];

      const processedBlocks: ProcessedBlock[] = [];
      let blockCounter = 0;
      const matches = Array.from(logContent.matchAll(BLOCK_REGEX));
      if (DEBUG) console.log(`[Worker] Found ${matches.length} potential blocks.`);

      for (const match of matches) {
          blockCounter++;
          const blockContent = match[0];
          const blockId = `block_${blockCounter}`;
          if (DEBUG) console.log(`[Worker] Extracted block ${blockId}, length ${blockContent.length}. Snippet: ${blockContent.substring(0,100)}...`);

          if (blockContent) {
              const processedBlock = processRawBlock(blockId, blockContent);
              if (processedBlock) {
                  if (DEBUG) console.log(`[Worker] Processed block ${blockId}. Glue values: ${processedBlock.glue_thickness_values.length}, Diff values: ${processedBlock.collimation_diff_values.length}`);
                  if (processedBlock.glue_thickness_values.length > 0 || processedBlock.collimation_diff_values.length > 0) {
                      processedBlocks.push(processedBlock);
                  } else {
                      if (DEBUG) console.log(`[Worker] Block ${blockId} skipped as it contained no glue or collimation values after processing.`);
                  }
              } else {
                   if (DEBUG) console.log(`[Worker] processRawBlock returned null for block ${blockId}.`);
              }
          }
      }
      if (DEBUG) console.log(`[Worker] parseLogContent finished. Total processed blocks with data: ${processedBlocks.length}`);
      return processedBlocks;
  }

  // Worker message handling
  self.onmessage = function(event: MessageEvent<string>) {
    console.log('[Worker] Message received in worker:', event);
    const logContent = event.data;

    if (!logContent || typeof logContent !== 'string') {
      console.error('[Worker] Invalid log content received:', logContent);
      const errorPayload = { error: 'Invalid log content received by worker.' };
      console.log('[Worker] Posting error message (invalid content):', errorPayload);
      self.postMessage(errorPayload);
      return;
    }

    console.log(`[Worker] Received log content. Length: ${logContent.length}. Starting parsing...`);

    try {
      const processedBlocks = parseLogContent(logContent);
      console.log(`[Worker] Parsing complete. Found ${processedBlocks.length} blocks with data.`);

      if (processedBlocks && processedBlocks.length > 0) {
        console.log(`[Worker] Sending ${processedBlocks.length} processed blocks to main thread.`);
        const successPayload = { success: true, allBlocks: processedBlocks, message: `Successfully processed ${processedBlocks.length} blocks.` };
        console.log('[Worker] Posting success message (with blocks):', JSON.stringify(successPayload)); // Log stringified payload
        self.postMessage(successPayload);
      } else {
        const message = 'No relevant data blocks with glue/collimation values were found in the log file after parsing.';
        console.log(`[Worker] ${message}`);
        const emptySuccessPayload = { success: true, allBlocks: [], message: message };
        console.log('[Worker] Posting success message (no blocks):', JSON.stringify(emptySuccessPayload)); // Log stringified payload
        self.postMessage(emptySuccessPayload);
      }
    } catch (error: any) {
      console.error('[Worker] Critical error during log processing in worker:', error);
      const criticalErrorPayload = { error: error.message || 'Unknown error in worker processing.' };
      console.log('[Worker] Posting critical error message:', JSON.stringify(criticalErrorPayload)); // Log stringified payload
      self.postMessage(criticalErrorPayload);
    }
  };

  self.onerror = function(errorEvent) {
      console.error('[Worker] Uncaught error in worker script:', errorEvent);
  };

  console.log('[Worker] logParser.worker.ts script loaded and event listener for "message" set up.');
}