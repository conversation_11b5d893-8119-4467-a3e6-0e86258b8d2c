'use client';

import React, { useState } from 'react';
import { SearchBar } from '@/components/surface-data-query/SearchBar';
import { ResultsDisplay } from '@/components/surface-data-query/ResultsDisplay';
import { toast } from '@/components/ui/use-toast';
import { SurfaceFile, SearchResult } from '@/types/surface-data';

export default function SurfaceDataQueryPage() {
  const [searchResults, setSearchResults] = useState<SurfaceFile[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<SurfaceFile[]>([]);

  const handleSearch = async (searchTerms: string, useRegex: boolean) => {
    setIsLoading(true);
    setError(null);
    setSearchResults([]);
    setSelectedFiles([]);

    try {
      const termsArray = searchTerms.split('\n').map(term => term.trim()).filter(term => term.length > 0);

      if (termsArray.length === 0) {
        toast({ title: "搜索条件不足", description: "请输入至少一个搜索条件。", variant: "destructive" });
        setIsLoading(false);
        return;
      }

      let combinedRegexPattern: string;
      if (useRegex) {
        // Each line is already a regex, combine with OR
        combinedRegexPattern = termsArray.join('|');
      } else {
        // Each line is a literal string, escape and combine with OR
        combinedRegexPattern = termsArray.map(term => term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('|');
      }
      
      const params = new URLSearchParams();
      params.append('regex', combinedRegexPattern);
      // Regarding basePath:
      // The API will use its default basePath (e.g., from FTP_BASE_PATH_PRISM or similar environment variable)
      // No need to send basePath from the client unless specific selection is implemented.

      const response = await fetch(`/api/ftp/search?${params.toString()}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const result: SearchResult = await response.json();
      setSearchResults(result.files);
      if (result.files.length === 0) {
        toast({
          title: '搜索结果',
          description: '未找到匹配的文件。',
        });
      } else if (result.files.length >= result.limit) { // Use result.limit for comparison
        toast({
          title: '搜索结果提示',
          description: `搜索结果已达到${result.limit}条上限，可能还有更多结果未显示。请优化搜索条件。`,
          variant: 'default',
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '搜索过程中发生未知错误';
      setError(errorMessage);
      toast({
        title: '搜索错误',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownloadSelected = async () => {
    if (selectedFiles.length === 0) {
      toast({
        title: '下载提示',
        description: '请至少选择一个文件进行下载。',
        variant: 'default',
      });
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const filePaths = selectedFiles.map(file => file.fullPath);
      const response = await fetch('/api/ftp/download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ filePaths }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      a.download = selectedFiles.length > 1 ? `surface_data_${timestamp}.zip` : selectedFiles[0].name;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);

      toast({
        title: '下载成功',
        description: `${selectedFiles.length} 个文件已开始下载。`,
      });
      setSelectedFiles([]); // Clear selection after download
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '下载过程中发生未知错误';
      setError(errorMessage);
      toast({
        title: '下载错误',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      <h1 className="text-2xl font-bold">面形数据查询</h1>
      <SearchBar onSearch={handleSearch} isLoading={isLoading} />
      {error && <div className="text-red-500">错误: {error}</div>}
      <ResultsDisplay
        results={searchResults}
        isLoading={isLoading}
        selectedFiles={selectedFiles}
        setSelectedFiles={setSelectedFiles}
        onDownloadSelected={handleDownloadSelected}
      />
    </div>
  );
}