{"format": 1, "restore": {"D:\\pycode\\support_chart2\\csharp-wrapper\\WrapperApp.csproj": {}}, "projects": {"D:\\pycode\\support_chart2\\csharp-wrapper\\WrapperApp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\pycode\\support_chart2\\csharp-wrapper\\WrapperApp.csproj", "projectName": "WrapperApp", "projectPath": "D:\\pycode\\support_chart2\\csharp-wrapper\\WrapperApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\pycode\\support_chart2\\csharp-wrapper\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}}