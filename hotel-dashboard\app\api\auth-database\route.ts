import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import crypto from 'crypto';
import os from 'os';

// 密码文件路径优先级（按顺序尝试）
const PASSWORD_FILE_PATHS = [
  // 1. 项目根目录（开发环境）
  path.join(process.cwd(), 'database-password.txt'),
  // 2. 系统配置目录（生产环境）
  path.join('C:', 'ProgramData', 'HotelDashboard', 'database-password.txt'),
  // 3. 用户目录
  path.join(os.homedir(), 'AppData', 'Local', 'HotelDashboard', 'database-password.txt'),
  // 4. 当前工作目录的上级目录
  path.join(process.cwd(), '..', 'database-password.txt'),
];

// 默认密码（如果文件不存在）
const DEFAULT_PASSWORD = 'admin123';

// 获取或创建密码文件
async function getStoredPassword(): Promise<{ password: string; filePath: string }> {
  // 按优先级尝试读取密码文件
  for (const filePath of PASSWORD_FILE_PATHS) {
    try {
      const password = await fs.readFile(filePath, 'utf-8');
      console.log(`找到密码文件: ${filePath}`);
      return { password: password.trim(), filePath };
    } catch (error) {
      // 继续尝试下一个路径
      continue;
    }
  }

  // 所有路径都失败，创建默认密码文件（使用第一个路径）
  const defaultPath = PASSWORD_FILE_PATHS[0];
  console.log(`密码文件不存在，在以下位置创建默认密码文件: ${defaultPath}`);

  try {
    // 确保目录存在
    await fs.mkdir(path.dirname(defaultPath), { recursive: true });
    await fs.writeFile(defaultPath, DEFAULT_PASSWORD, 'utf-8');
    return { password: DEFAULT_PASSWORD, filePath: defaultPath };
  } catch (error) {
    console.error('创建密码文件失败:', error);
    throw new Error('无法创建密码文件');
  }
}

// 保存密码到文件
async function savePassword(newPassword: string, filePath: string): Promise<void> {
  try {
    // 确保目录存在
    await fs.mkdir(path.dirname(filePath), { recursive: true });
    await fs.writeFile(filePath, newPassword, 'utf-8');
  } catch (error) {
    console.error('保存密码文件失败:', error);
    throw new Error('无法保存密码文件');
  }
}

// 生成密码哈希（简单的哈希，实际项目中应使用更安全的方法）
function hashPassword(password: string): string {
  return crypto.createHash('sha256').update(password).digest('hex');
}

export async function POST(request: NextRequest) {
  try {
    const { password, action } = await request.json();

    if (action === 'verify') {
      // 验证密码
      const { password: storedPassword } = await getStoredPassword();
      const isValid = password === storedPassword;

      if (isValid) {
        return NextResponse.json({
          success: true,
          message: '密码验证成功'
        });
      } else {
        return NextResponse.json({
          success: false,
          message: '密码错误'
        }, { status: 401 });
      }
    } else if (action === 'change') {
      // 更改密码（需要提供旧密码）
      const { oldPassword, newPassword } = await request.json();
      const { password: storedPassword, filePath } = await getStoredPassword();

      if (oldPassword !== storedPassword) {
        return NextResponse.json({
          success: false,
          message: '原密码错误'
        }, { status: 401 });
      }

      // 保存新密码
      await savePassword(newPassword, filePath);

      return NextResponse.json({
        success: true,
        message: '密码修改成功'
      });
    } else {
      return NextResponse.json({ 
        success: false, 
        message: '无效的操作' 
      }, { status: 400 });
    }

  } catch (error: any) {
    console.error('密码验证API错误:', error);
    return NextResponse.json({ 
      success: false, 
      message: '服务器错误: ' + error.message 
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    // 检查密码文件是否存在
    const { password: storedPassword, filePath } = await getStoredPassword();

    return NextResponse.json({
      success: true,
      message: '密码文件已就绪',
      hasPassword: true,
      filePath: filePath,
      defaultPassword: storedPassword === DEFAULT_PASSWORD ? DEFAULT_PASSWORD : null
    });
  } catch (error: any) {
    console.error('获取密码状态错误:', error);
    return NextResponse.json({
      success: false,
      message: '服务器错误: ' + error.message
    }, { status: 500 });
  }
}
