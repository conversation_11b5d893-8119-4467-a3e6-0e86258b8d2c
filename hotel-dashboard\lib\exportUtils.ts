import domtoimage from 'dom-to-image-more';
import { toast } from '../components/ui/use-toast'; // 确认路径
import { saveAs } from 'file-saver';
import JSZip from 'jszip';

// downloadImage 函数保持不变
function downloadImage(dataUrl: string, fileName: string): void {
  const link = document.createElement('a');
  link.href = dataUrl;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  link.remove();
}

interface ExportProgressCallback {
  (progress: number): void;
}

// Helper function to wait for chart readiness
async function waitForChartReady(chartElement: HTMLElement, blockId: string, timeout: number = 3000): Promise<void> {
  const startTime = Date.now();
  return new Promise((resolve, reject) => {
    const check = () => {
      const isLoadingTextPresent = chartElement.innerText.includes("图表加载中...");
      const hasRechartsSurface = chartElement.querySelector('.recharts-surface');

      if (hasRechartsSurface && !isLoadingTextPresent) {
        console.log(`Chart for block ${blockId} is ready.`);
        resolve();
      } else if (Date.now() - startTime > timeout) {
        console.warn(`Timeout waiting for chart ${blockId} to render. isLoadingTextPresent: ${isLoadingTextPresent}, hasRechartsSurface: ${!!hasRechartsSurface}`);
        // Resolve anyway to attempt capture, or reject to mark as error?
        // For now, resolve and let domtoimage try, but log a warning.
        // Consider rejecting if a clean image is strictly required.
        resolve(); // Or reject(new Error(...));
      } else {
        setTimeout(check, 200); // Check every 200ms
      }
    };
    check();
  });
}

export async function exportElementAsImage(
  element: HTMLElement, // This is the parent container where charts are rendered
  filename: string, // Base filename for the ZIP
  selectedBlockIds: string[],
  onProgress?: ExportProgressCallback
): Promise<void> {
  try {
    console.log('[exportUtils] Starting export for ZIP. Selected block IDs:', selectedBlockIds);
    const zip = new JSZip();
    const total = selectedBlockIds.length;
    let completed = 0;
    let errorsEncountered = 0;

    // 为每个选中的数据块创建图片
    for (const blockId of selectedBlockIds) {
      console.log(`[exportUtils] Processing block ${blockId}`);
      const blockElement = element.querySelector(`[data-block-id="${blockId}"]`) as HTMLElement | null;
      
      if (!blockElement) {
        console.error(`[exportUtils] Block element not found for ID: ${blockId}. Skipping.`);
        zip.file(`error_block-${blockId}_not_found.txt`, `DOM element for block ID ${blockId} was not found.`);
        completed++;
        errorsEncountered++;
        if (onProgress) {
          onProgress((completed / total) * 100);
        }
        continue;
      }

      try {
        console.log(`[exportUtils] Found block element for ${blockId}. Waiting for chart to be ready...`);
        await waitForChartReady(blockElement, blockId); // Wait for the specific chart to render

        console.log(`[exportUtils] Chart for ${blockId} is considered ready. Generating image...`);
        const dataUrl = await domtoimage.toPng(blockElement, {
          width: blockElement.scrollWidth,
          height: blockElement.scrollHeight,
        bgcolor: '#ffffff',
        style: {
          'border': 'none !important',
          'outline': 'none !important',
          'box-shadow': 'none !important',
          'background-color': '#ffffff !important'
        },
        filter: (node: Node) => {
          if (node instanceof HTMLElement) {
            node.style.border = 'none';
            node.style.outline = 'none';
            node.style.boxShadow = 'none';
          }
          return true;
        },
        cacheBust: true
      });

        console.log(`[exportUtils] Image generated for block ${blockId}.`);
        const base64Data = dataUrl.split(',')[1];
        if (!base64Data) {
            throw new Error(`Failed to extract base64 data for block ${blockId}`);
        }
        const binaryData = atob(base64Data);
        const array = new Uint8Array(binaryData.length);
        for (let i = 0; i < binaryData.length; i++) {
          array[i] = binaryData.charCodeAt(i);
        }
        zip.file(`chart_block-${blockId}.png`, array);
        console.log(`[exportUtils] Added image for block ${blockId} to zip.`);
      } catch (imgError) {
        console.error(`[exportUtils] Failed to generate or add image for block ${blockId}:`, imgError);
        zip.file(`error_block-${blockId}_img_generation.txt`, `Failed to generate image for block ${blockId}: ${imgError instanceof Error ? imgError.message : String(imgError)}`);
        errorsEncountered++;
      }

      completed++;
      if (onProgress) {
        onProgress((completed / total) * 100);
      }
      console.log(`[exportUtils] Finished processing block ${blockId}. Progress: ${(completed / total) * 100}%`);
    }

    if (completed === 0 && total > 0) {
        throw new Error('No blocks were processed for export.');
    }
    
    if (total === 0) {
        toast({
            title: "没有内容可导出",
            description: "没有选择任何数据块进行导出。",
            variant: "default"
        });
        return;
    }

    console.log('[exportUtils] Generating zip file...');
    const content = await zip.generateAsync({ type: 'blob' });
    console.log('[exportUtils] Zip file generated, size:', content.size);
    
    if (content.size === 0) {
      // This case should ideally be caught by earlier checks if no files were added.
      throw new Error('Generated zip file is empty. This might happen if all image generations failed.');
    }

    saveAs(content, `${filename}.zip`);
    console.log('[exportUtils] Zip file saved.');

    if (errorsEncountered > 0) {
        toast({
            title: "导出部分完成",
            description: `${total - errorsEncountered} 个图表已导出，但有 ${errorsEncountered} 个图表导出失败。详情请查看ZIP包内的错误文件。`,
            variant: "default", // Changed "warning" to "default"
        });
    } else {
        toast({
          title: "导出成功",
          description: `已将 ${total} 个图表导出为压缩包。`,
        });
    }
  } catch (error) {
    console.error('[exportUtils] Error exporting elements as images:', error);
    toast({
      title: "导出失败",
      description: error instanceof Error ? error.message : '导出过程中发生错误',
      variant: "destructive",
    });
    throw error;
  }
}