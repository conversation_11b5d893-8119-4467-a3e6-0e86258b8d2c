"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { toast } from "@/components/ui/use-toast";
import { Download, Database, RefreshCw, Filter, Calendar, Hash, Type, CalendarIcon, Lock, Eye, EyeOff } from "lucide-react";
import { format } from "date-fns";

// Define a type for the API response
type QueryResult = {
  [tableName: string]: Record<string, any>[];
};

// Define types for column information
type ColumnInfo = {
  name: string;
  type: string;
  dataType: 'string' | 'number' | 'date' | 'boolean';
};

// Define types for filter conditions
type FilterCondition = {
  column: string;
  operator: string;
  value: string | number | [string | number, string | number];
  enabled: boolean;
};

type StringFilterOptions = {
  useRegex: boolean;
  exactMatch: boolean;
};

export default function DatabaseQueryPage() {

  const [result, setResult] = useState<QueryResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [tables, setTables] = useState<string[]>([]);
  const [isLoadingTables, setIsLoadingTables] = useState(false);

  // 新增状态：结构化查询相关
  const [selectedTable, setSelectedTable] = useState<string>("");
  const [columns, setColumns] = useState<ColumnInfo[]>([]);
  const [isLoadingColumns, setIsLoadingColumns] = useState(false);
  const [filters, setFilters] = useState<FilterCondition[]>([]);

  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [totalRows, setTotalRows] = useState(0);
  const [pageSize] = useState(100);
  const [showPagination, setShowPagination] = useState(false);

  // 导出进度相关状态
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportTotal, setExportTotal] = useState(0);

  // 密码验证相关状态
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);

  // 密码验证函数
  const verifyPassword = async () => {
    if (!password.trim()) {
      toast({
        title: "错误",
        description: "请输入密码",
        variant: "destructive",
      });
      return;
    }

    setIsVerifying(true);
    try {
      const response = await fetch('/api/auth-database', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          password: password.trim(),
          action: 'verify'
        }),
      });

      const result = await response.json();

      if (result.success) {
        setIsAuthenticated(true);
        setPassword(""); // 清空密码输入
        toast({
          title: "验证成功",
          description: "欢迎使用数据库查询功能",
        });
      } else {
        toast({
          title: "验证失败",
          description: result.message || "密码错误",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error('密码验证错误:', error);
      toast({
        title: "验证错误",
        description: "网络错误，请重试",
        variant: "destructive",
      });
    } finally {
      setIsVerifying(false);
    }
  };

  // 退出登录
  const logout = () => {
    setIsAuthenticated(false);
    setPassword("");
    setResult(null);
    setError(null);
    setSelectedTable("");
    setFilters([]);
    setCurrentPage(1);
    setTotalRows(0);
    setShowPagination(false);
  };

  // CSV导出函数
  const exportToCSV = (tableName: string, data: Record<string, any>[]) => {
    if (data.length === 0) {
      toast({
        title: "无数据",
        description: "没有数据可导出。",
        variant: "destructive",
      });
      return;
    }

    const headers = Object.keys(data[0]);

    // 创建CSV内容
    const csvContent = [
      // CSV头部
      headers.join(','),
      // CSV数据行
      ...data.map(row =>
        headers.map(header => {
          const value = String(row[header] || '');
          // 如果值包含逗号、引号或换行符，需要用引号包围并转义引号
          if (value.includes(',') || value.includes('"') || value.includes('\n')) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        }).join(',')
      )
    ].join('\n');

    // 创建Blob并下载
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', `${tableName}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`);
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "导出成功",
      description: `${tableName} 已成功导出到CSV文件。`,
    });
  };

  // 分批导出所有数据的函数
  const exportAllDataToCSV = async (tableName: string) => {
    console.log('开始导出数据，表名:', tableName);
    setIsExporting(true);
    setExportProgress(0);

    try {
      // 1. 先获取总行数
      const countQuery = buildStructuredQuery({ withCount: true }) || `SELECT COUNT(*) as total FROM ${tableName};`;
      console.log('获取导出总行数:', countQuery);

      const countResponse = await fetch('/api/database-query', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: countQuery }),
      });

      const countData = await countResponse.json();
      console.log('导出COUNT查询响应:', countData);

      let totalCount = 0;
      if (countResponse.ok && countData.data) {
        console.log('导出COUNT查询数据结构:', countData.data);

        // 使用与显示查询相同的解析逻辑
        if (typeof countData.data === 'object' && !Array.isArray(countData.data)) {
          // 处理 {tableName: [{total: 1500}]} 格式
          const tableNames = Object.keys(countData.data);
          if (tableNames.length > 0) {
            const firstTableData = countData.data[tableNames[0]] as any[];
            if (firstTableData && firstTableData.length > 0) {
              totalCount = parseInt(firstTableData[0].total) || 0;
            }
          }
        } else if (Array.isArray(countData.data) && countData.data.length > 0) {
          // 处理直接数组格式
          totalCount = parseInt(countData.data[0].total) || 0;
        }

        console.log('导出解析的总行数:', totalCount);
      } else {
        console.error('导出COUNT查询失败:', countData);
      }

      if (totalCount === 0) {
        toast({
          title: "无数据",
          description: "没有数据可导出。",
          variant: "destructive",
        });
        return;
      }

      setExportTotal(totalCount);

      // 2. 分批查询数据
      const batchSize = 100;
      const totalBatches = Math.ceil(totalCount / batchSize);
      let allData: Record<string, any>[] = [];
      let headers: string[] = [];


      for (let batch = 0; batch < totalBatches; batch++) {
        const offset = batch * batchSize;
        const batchQuery = buildStructuredQuery({ page: batch + 1 }) ||
                          `SELECT * FROM ${tableName} LIMIT ${batchSize} OFFSET ${offset};`;

        console.log(`导出批次 ${batch + 1}/${totalBatches}:`, batchQuery);

        const batchResponse = await fetch('/api/database-query', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ query: batchQuery }),
        });

        const batchData = await batchResponse.json();

        if (batchResponse.ok && batchData.data) {
          // 假设数据结构是 { tableName: [...] }
          const tableData = Object.values(batchData.data)[0] as Record<string, any>[];
          if (tableData && Array.isArray(tableData)) {
            if (batch === 0) {
              headers = Object.keys(tableData[0] || {});
            }
            allData = allData.concat(tableData);
          }
        }

        // 更新进度
        setExportProgress(batch + 1);

        // 给UI一点时间更新
        await new Promise(resolve => setTimeout(resolve, 10));
      }

      // 3. 生成CSV并下载
      if (allData.length > 0) {
        exportToCSV(tableName, allData);
        toast({
          title: "导出完成",
          description: `成功导出 ${allData.length} 条记录`,
        });
      } else {
        toast({
          title: "导出失败",
          description: "未能获取到数据",
          variant: "destructive",
        });
      }

    } catch (err: any) {
      console.error('导出错误:', err);
      toast({
        title: "导出失败",
        description: err.message,
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
      setExportProgress(0);
      setExportTotal(0);
    }
  };

  // 获取数据库表列表
  const fetchTables = async () => {
    setIsLoadingTables(true);

    try {
      // 尝试多种不同的查询方式
      const queries = [
        "USE gina_db; SHOW TABLES;",
        "SHOW TABLES FROM gina_db;",
        "SELECT name FROM gina_db.sys.tables ORDER BY name;",
        "SELECT TABLE_NAME FROM gina_db.INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME;",
      ];

      for (let i = 0; i < queries.length; i++) {
        try {
          console.log(`Trying query ${i + 1}: ${queries[i]}`);

          const response = await fetch('/api/database-query', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              query: queries[i]
            }),
          });

          const response_data = await response.json();

          if (response.ok) {
            console.log('Query succeeded:', response_data);

            // 提取表名列表
            const tableData = response_data.data;
            if (tableData && Object.keys(tableData).length > 0) {
              const firstKey = Object.keys(tableData)[0];
              const tableRows = tableData[firstKey];
              if (Array.isArray(tableRows) && tableRows.length > 0) {
                // 尝试不同的列名
                const possibleColumns = ['TABLE_NAME', 'name', 'Tables_in_gina_db'];
                let tableNames: string[] = [];

                // 首先尝试已知的列名
                for (const colName of possibleColumns) {
                  if (tableRows[0].hasOwnProperty(colName)) {
                    tableNames = tableRows.map((row: any) => String(row[colName])).filter(Boolean);
                    break;
                  }
                }

                // 如果没有找到已知列名，使用第一列
                if (tableNames.length === 0) {
                  tableNames = tableRows.map((row: any) => {
                    const values = Object.values(row);
                    return values.length > 0 ? String(values[0]) : null;
                  }).filter((name): name is string => Boolean(name));
                }

                if (tableNames.length > 0) {
                  setTables(tableNames);
                  console.log('Found tables:', tableNames);
                  return; // 成功获取表列表，退出函数
                }
              }
            }
          } else {
            console.log(`Query ${i + 1} failed:`, response_data.error);
          }
        } catch (err) {
          console.log(`Query ${i + 1} error:`, err);
          continue; // 尝试下一个查询
        }
      }

      // 所有查询都失败了
      throw new Error('所有表列表查询都失败了。请检查数据库连接和权限。');

    } catch (err: any) {
      console.error('Error fetching tables:', err);
      toast({
        title: "错误",
        description: "获取表列表失败: " + err.message,
        variant: "destructive",
      });
    } finally {
      setIsLoadingTables(false);
    }
  };

  // 获取表的列信息
  const fetchColumns = async (tableName: string) => {
    if (!tableName) return;

    setIsLoadingColumns(true);
    try {
      const response = await fetch('/api/database-query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '${tableName}' ORDER BY ORDINAL_POSITION;`
        }),
      });

      const response_data = await response.json();

      if (response.ok) {
        const tableData = response_data.data;
        if (tableData && Object.keys(tableData).length > 0) {
          const firstKey = Object.keys(tableData)[0];
          const columnRows = tableData[firstKey];
          if (Array.isArray(columnRows)) {
            const columnInfo: ColumnInfo[] = columnRows.map((row: any) => {
              const columnName = row.COLUMN_NAME || row.column_name || '';
              const dataType = (row.DATA_TYPE || row.data_type || '').toLowerCase();

              // 根据数据类型判断字段类型
              let fieldType: 'string' | 'number' | 'date' | 'boolean' = 'string';
              if (dataType.includes('int') || dataType.includes('decimal') || dataType.includes('float') ||
                  dataType.includes('numeric') || dataType.includes('double') || dataType.includes('real') ||
                  dataType.includes('money') || dataType.includes('smallmoney')) {
                fieldType = 'number';
              } else if (dataType.includes('date') || dataType.includes('time') || dataType.includes('timestamp')) {
                fieldType = 'date';
              } else if (dataType.includes('bit') || dataType.includes('boolean')) {
                fieldType = 'boolean';
              }

              return {
                name: columnName,
                type: dataType,
                dataType: fieldType
              };
            });

            // 去重处理，以防有重复的列名
            const uniqueColumns = columnInfo.filter((col, index, self) =>
              index === self.findIndex(c => c.name === col.name)
            );

            setColumns(uniqueColumns);
            console.log('Found columns:', uniqueColumns);
          }
        }
      } else {
        throw new Error(response_data.error || '获取列信息失败。');
      }
    } catch (err: any) {
      console.error('Error fetching columns:', err);
      toast({
        title: "错误",
        description: "获取列信息失败: " + err.message,
        variant: "destructive",
      });
    } finally {
      setIsLoadingColumns(false);
    }
  };

  // 页面加载时获取表列表
  useEffect(() => {
    fetchTables();
  }, []);

  // 处理表选择
  const handleTableSelect = (tableName: string) => {
    setSelectedTable(tableName);
    setFilters([]); // 清空之前的筛选条件
    // 重置分页状态
    setCurrentPage(1);
    setTotalRows(0);
    setShowPagination(false);
    setResult(null);
    setError(null);
    fetchColumns(tableName); // 获取列信息
  };

  // 构建结构化查询
  const buildStructuredQuery = ({ withCount = false, page = 1 } = {}) => {
    if (!selectedTable) return "";

    let whereClause = "";
    const activeFilters = filters.filter(f => f.enabled && f.value !== "" && f.value !== null);

    if (activeFilters.length > 0) {
      const conditions = activeFilters.map(filter => {
        const column = filter.column;
        const value = filter.value;

        switch (filter.operator) {
          case 'equals':
            return `${column} = '${value}'`;
          case 'contains':
            return `${column} LIKE '%${value}%'`;
          case 'starts_with':
            return `${column} LIKE '${value}%'`;
          case 'ends_with':
            return `${column} LIKE '%${value}'`;
          case 'regex':
            return `${column} LIKE '%${value}%'`; // 使用LIKE代替REGEXP以提高兼容性
          case 'greater_than':
            return `${column} > ${value}`;
          case 'less_than':
            return `${column} < ${value}`;
          case 'between':
            if (Array.isArray(value) && value.length === 2) {
              return `${column} BETWEEN ${value[0]} AND ${value[1]}`;
            }
            return `${column} = ${value}`;
          case 'date_range':
            if (Array.isArray(value) && value.length === 2) {
              return `${column} BETWEEN '${value[0]}' AND '${value[1]}'`;
            }
            return `${column} = '${value}'`;
          default:
            return `${column} = '${value}'`;
        }
      });

      whereClause = " WHERE " + conditions.join(" AND ");
    }

    if (withCount) {
      return `SELECT COUNT(*) as total FROM ${selectedTable}${whereClause};`;
    }

    const offset = (page - 1) * pageSize;
    return `SELECT * FROM ${selectedTable}${whereClause} LIMIT ${pageSize} OFFSET ${offset};`;
  };

  // 执行结构化查询
  const handleStructuredQuery = async (page = 1) => {
    const structuredQuery = buildStructuredQuery({ page });

    // 直接执行查询，不依赖状态更新
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      // 首先执行COUNT查询获取总行数
      const countQuery = buildStructuredQuery({ withCount: true });
      console.log('执行COUNT查询:', countQuery);

      const countResponse = await fetch('/api/database-query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query: countQuery }),
      });

      const countData = await countResponse.json();
      console.log('COUNT查询响应:', countData);

      let totalCount = 0;
      if (countResponse.ok && countData.data) {
        console.log('COUNT查询数据结构:', countData.data);

        // 检查数据结构，可能是 { tableName: [...] } 格式
        if (typeof countData.data === 'object' && !Array.isArray(countData.data)) {
          // 如果是对象格式，取第一个表的数据
          const firstTableData = Object.values(countData.data)[0] as any[];
          if (firstTableData && firstTableData.length > 0) {
            totalCount = parseInt(firstTableData[0].total) || 0;
          }
        } else if (Array.isArray(countData.data) && countData.data.length > 0) {
          // 如果是数组格式
          totalCount = parseInt(countData.data[0].total) || 0;
        }

        console.log('解析的总行数:', totalCount);
      }

      setTotalRows(totalCount);
      setShowPagination(true); // 始终显示分页信息，包括总数

      // 然后执行数据查询
      const response = await fetch('/api/database-query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query: structuredQuery }),
      });

      const response_data = await response.json();

      if (response.ok) {
        setResult(response_data.data);
        setCurrentPage(page);
      } else {
        throw new Error(response_data.error || '查询失败。');
      }
    } catch (err: any) {
      setError(err.message);
      toast({
        title: "错误",
        description: err.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };



  // 添加筛选条件
  const addFilter = () => {
    if (columns.length === 0) return;

    const newFilter: FilterCondition = {
      column: columns[0].name,
      operator: 'equals',
      value: '',
      enabled: true
    };

    setFilters([...filters, newFilter]);
  };

  // 更新筛选条件
  const updateFilter = (index: number, updates: Partial<FilterCondition>) => {
    const newFilters = [...filters];
    newFilters[index] = { ...newFilters[index], ...updates };
    setFilters(newFilters);
  };

  // 删除筛选条件
  const removeFilter = (index: number) => {
    const newFilters = filters.filter((_, i) => i !== index);
    setFilters(newFilters);
  };

  const renderTable = (tableName: string, data: Record<string, any>[]) => {
    if (data.length === 0) {
      return <p key={tableName}>表 '{tableName}' 没有数据行。</p>;
    }
    const headers = Object.keys(data[0]);
    return (
      <Card key={tableName} className="mt-4">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {tableName}
              <span className="text-sm text-gray-500 font-normal">
                ({data.length} rows)
              </span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => exportAllDataToCSV(tableName)}
              disabled={isExporting}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              {isExporting ? "导出中..." : "导出全部数据"}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {/* 导出进度条 */}
          {isExporting && (
            <div className="p-4 border-b bg-gray-50">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">导出进度</span>
                <span className="text-sm text-gray-600">
                  {exportProgress} / {Math.ceil(exportTotal / 100)} 批次
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${exportTotal > 0 ? (exportProgress / Math.ceil(exportTotal / 100)) * 100 : 0}%`
                  }}
                ></div>
              </div>
              <div className="text-xs text-gray-500 mt-1">
                正在导出 {exportTotal} 条记录...
              </div>
            </div>
          )}
          {/* 水平和垂直滚动容器 */}
          <div className="overflow-auto max-h-[600px] border rounded-md">
            <Table>
              <TableHeader className="sticky top-0 bg-white z-10 shadow-sm">
                <TableRow>
                  {headers.map((header) => (
                    <TableHead
                      key={header}
                      className="whitespace-nowrap px-4 py-3 font-semibold bg-gray-50 border-b-2"
                      style={{ minWidth: '120px' }}
                    >
                      {header}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.map((row, rowIndex) => (
                  <TableRow key={rowIndex} className="hover:bg-gray-50">
                    {headers.map((header) => (
                      <TableCell
                        key={`${rowIndex}-${header}`}
                        className="whitespace-nowrap px-4 py-2 text-sm border-b"
                        style={{ minWidth: '120px' }}
                        title={String(row[header])} // 鼠标悬停显示完整内容
                      >
                        <div className="max-w-[200px] truncate">
                          {String(row[header])}
                        </div>
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          {/* 显示分页信息 */}
          <div className="px-4 py-2 text-xs text-gray-500 bg-gray-50 border-t">
            {showPagination ? (
              `显示第 ${(currentPage - 1) * pageSize + 1}-${Math.min(currentPage * pageSize, totalRows)} 条，总计 ${totalRows} 条记录`
            ) : (
              `总计: ${data.length} 条记录`
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  // 如果未认证，显示密码验证界面
  if (!isAuthenticated) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 justify-center">
              <Lock className="h-5 w-5" />
              数据库访问验证
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center text-sm text-gray-600 mb-4">
              请输入密码以访问数据库查询功能
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">密码</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="请输入访问密码"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      verifyPassword();
                    }
                  }}
                  disabled={isVerifying}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isVerifying}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            <Button
              onClick={verifyPassword}
              disabled={isVerifying || !password.trim()}
              className="w-full"
            >
              {isVerifying ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  验证中...
                </>
              ) : (
                <>
                  <Lock className="mr-2 h-4 w-4" />
                  验证密码
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              数据库查询
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={logout}
              className="flex items-center gap-2"
            >
              <Lock className="h-4 w-4" />
              退出
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* 表选择下拉列表 */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">
              <Database className="inline h-4 w-4 mr-1" />
              快速表选择 (gina_db)
            </label>
            <div className="flex gap-2">
              <Select onValueChange={handleTableSelect}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={isLoadingTables ? "加载表中..." : "选择要查询的表"} />
                </SelectTrigger>
                <SelectContent>
                  {tables.map((tableName) => (
                    <SelectItem key={tableName} value={tableName}>
                      {tableName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchTables}
                disabled={isLoadingTables}
                className="flex items-center gap-1"
              >
                <RefreshCw className={`h-4 w-4 ${isLoadingTables ? 'animate-spin' : ''}`} />
                刷新
              </Button>
            </div>
            {tables.length > 0 && (
              <p className="text-xs text-gray-500 mt-1">
                在 gina_db 数据库中找到 {tables.length} 个表
              </p>
            )}
          </div>

          {/* 结构化查询界面 */}
          {selectedTable && (
            <div className="mb-4">
              <div className="flex items-center justify-between mb-4">
                <Label className="text-sm font-medium flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  {selectedTable} 的筛选条件
                </Label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={addFilter}
                  disabled={isLoadingColumns || columns.length === 0}
                >
                  添加筛选
                </Button>
              </div>

              {isLoadingColumns && (
                <div className="text-sm text-gray-500 mb-2">加载列中...</div>
              )}

              {filters.length > 0 && (
                <div className="space-y-3 mb-4">
                  {filters.map((filter, index) => (
                    <div key={index} className="flex items-center gap-2 p-3 border rounded-lg">
                      <Switch
                        checked={filter.enabled}
                        onCheckedChange={(enabled) => updateFilter(index, { enabled })}
                      />

                      <Select
                        value={filter.column}
                        onValueChange={(column) => updateFilter(index, { column })}
                      >
                        <SelectTrigger className="w-40">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {columns.map((col, colIndex) => (
                            <SelectItem key={`${col.name}-${colIndex}`} value={col.name}>
                              <div className="flex items-center gap-2">
                                {col.dataType === 'string' && <Type className="h-3 w-3" />}
                                {col.dataType === 'number' && <Hash className="h-3 w-3" />}
                                {col.dataType === 'date' && <Calendar className="h-3 w-3" />}
                                {col.name} ({col.type})
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      <Select
                        value={filter.operator}
                        onValueChange={(operator) => updateFilter(index, { operator })}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {(() => {
                            const selectedColumn = columns.find(col => col.name === filter.column);
                            if (selectedColumn?.dataType === 'string') {
                              return [
                                <SelectItem key="equals" value="equals">等于</SelectItem>,
                                <SelectItem key="contains" value="contains">包含</SelectItem>,
                                <SelectItem key="starts_with" value="starts_with">开头是</SelectItem>,
                                <SelectItem key="ends_with" value="ends_with">结尾是</SelectItem>,
                                <SelectItem key="regex" value="regex">正则表达式</SelectItem>
                              ];
                            } else if (selectedColumn?.dataType === 'number') {
                              return [
                                <SelectItem key="equals" value="equals">等于</SelectItem>,
                                <SelectItem key="greater_than" value="greater_than">大于</SelectItem>,
                                <SelectItem key="less_than" value="less_than">小于</SelectItem>,
                                <SelectItem key="between" value="between">范围</SelectItem>
                              ];
                            } else if (selectedColumn?.dataType === 'date') {
                              return [
                                <SelectItem key="equals" value="equals">等于</SelectItem>,
                                <SelectItem key="date_range" value="date_range">日期范围</SelectItem>
                              ];
                            }
                            return [<SelectItem key="equals" value="equals">等于</SelectItem>];
                          })()}
                        </SelectContent>
                      </Select>

                      {filter.operator === 'between' || filter.operator === 'date_range' ? (
                        <div className="flex items-center gap-1">
                          {filter.operator === 'date_range' ? (
                            <>
                              <Popover>
                                <PopoverTrigger asChild>
                                  <Button
                                    variant="outline"
                                    className="w-32 justify-start text-left font-normal"
                                  >
                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                    {Array.isArray(filter.value) && filter.value[0]
                                      ? format(new Date(filter.value[0]), "yyyy-MM-dd")
                                      : "开始日期"
                                    }
                                  </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0">
                                  <CalendarComponent
                                    mode="single"
                                    selected={Array.isArray(filter.value) && filter.value[0] ? new Date(filter.value[0]) : undefined}
                                    onSelect={(date) => {
                                      const currentValue = Array.isArray(filter.value) ? filter.value : ['', ''];
                                      const dateStr = date ? format(date, "yyyy-MM-dd") : '';
                                      updateFilter(index, { value: [dateStr, currentValue[1]] });
                                    }}
                                    initialFocus
                                  />
                                </PopoverContent>
                              </Popover>
                              <span className="text-sm text-gray-500">to</span>
                              <Popover>
                                <PopoverTrigger asChild>
                                  <Button
                                    variant="outline"
                                    className="w-32 justify-start text-left font-normal"
                                  >
                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                    {Array.isArray(filter.value) && filter.value[1]
                                      ? format(new Date(filter.value[1]), "yyyy-MM-dd")
                                      : "结束日期"
                                    }
                                  </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0">
                                  <CalendarComponent
                                    mode="single"
                                    selected={Array.isArray(filter.value) && filter.value[1] ? new Date(filter.value[1]) : undefined}
                                    onSelect={(date) => {
                                      const currentValue = Array.isArray(filter.value) ? filter.value : ['', ''];
                                      const dateStr = date ? format(date, "yyyy-MM-dd") : '';
                                      updateFilter(index, { value: [currentValue[0], dateStr] });
                                    }}
                                    initialFocus
                                  />
                                </PopoverContent>
                              </Popover>
                            </>
                          ) : (
                            <>
                              <Input
                                type="text"
                                placeholder="最小值"
                                className="w-24"
                                value={Array.isArray(filter.value) ? filter.value[0] : ''}
                                onChange={(e) => {
                                  const currentValue = Array.isArray(filter.value) ? filter.value : ['', ''];
                                  updateFilter(index, { value: [e.target.value, currentValue[1]] });
                                }}
                              />
                              <span className="text-sm text-gray-500">到</span>
                              <Input
                                type="text"
                                placeholder="最大值"
                                className="w-24"
                                value={Array.isArray(filter.value) ? filter.value[1] : ''}
                                onChange={(e) => {
                                  const currentValue = Array.isArray(filter.value) ? filter.value : ['', ''];
                                  updateFilter(index, { value: [currentValue[0], e.target.value] });
                                }}
                              />
                            </>
                          )}
                        </div>
                      ) : (
                        <Input
                          type={columns.find(col => col.name === filter.column)?.dataType === 'number' ? 'number' : 'text'}
                          placeholder="值"
                          className="flex-1"
                          value={Array.isArray(filter.value) ? '' : filter.value}
                          onChange={(e) => updateFilter(index, { value: e.target.value })}
                        />
                      )}

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeFilter(index)}
                      >
                        删除
                      </Button>
                    </div>
                  ))}
                </div>
              )}

              <div className="flex gap-2">
                <Button onClick={() => {
                  // 重置到第一页并执行查询
                  setCurrentPage(1);
                  handleStructuredQuery(1);
                }} disabled={isLoading} className="flex-1">
                  {isLoading ? "查询中..." : "使用筛选查询"}
                </Button>
                <Button
                  variant="outline"
                  onClick={async () => {
                    // 显示前100行数据（重置筛选条件），但先获取总数并启用分页
                    setFilters([]);
                    setCurrentPage(1);
                    setIsLoading(true);
                    setError(null);
                    setResult(null);

                    try {
                      // 先获取总行数
                      const countQuery = `SELECT COUNT(*) as total FROM ${selectedTable};`;
                      console.log('获取总行数:', countQuery);

                      const countResponse = await fetch('/api/database-query', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ query: countQuery }),
                      });

                      const countData = await countResponse.json();
                      console.log('COUNT查询响应:', countData);
                      console.log('COUNT查询响应状态:', countResponse.ok);

                      let totalCount = 0;
                      if (countResponse.ok && countData.data) {
                        console.log('COUNT查询数据结构:', countData.data);
                        console.log('数据类型:', typeof countData.data);
                        console.log('是否为数组:', Array.isArray(countData.data));

                        // 尝试多种数据结构解析
                        try {
                          // 方法1: 检查是否是 { tableName: [...] } 格式
                          if (typeof countData.data === 'object' && !Array.isArray(countData.data)) {
                            console.log('尝试对象格式解析...');
                            const tableNames = Object.keys(countData.data);
                            console.log('表名:', tableNames);

                            if (tableNames.length > 0) {
                              const firstTableData = countData.data[tableNames[0]] as any[];
                              console.log('第一个表的数据:', firstTableData);

                              if (firstTableData && firstTableData.length > 0) {
                                console.log('第一行数据:', firstTableData[0]);
                                totalCount = parseInt(firstTableData[0].total) || 0;
                              }
                            }
                          }
                          // 方法2: 检查是否是直接数组格式
                          else if (Array.isArray(countData.data) && countData.data.length > 0) {
                            console.log('尝试数组格式解析...');
                            console.log('第一行数据:', countData.data[0]);
                            totalCount = parseInt(countData.data[0].total) || 0;
                          }

                          console.log('解析的总行数:', totalCount);
                        } catch (parseError) {
                          console.error('解析COUNT数据时出错:', parseError);
                        }
                      } else {
                        console.error('COUNT查询失败:', countData);
                      }

                      setTotalRows(totalCount);
                      setShowPagination(true); // 始终显示分页信息，包括总数

                      // 然后查询前100行
                      const dataQuery = `SELECT * FROM ${selectedTable} LIMIT 100 OFFSET 0;`;

                      const dataResponse = await fetch('/api/database-query', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ query: dataQuery }),
                      });

                      const dataResult = await dataResponse.json();
                      if (dataResponse.ok) {
                        setResult(dataResult.data);
                        toast({
                          title: "显示全部数据",
                          description: `显示前100行，总计 ${totalCount} 条记录`,
                        });
                      } else {
                        throw new Error(dataResult.error || '查询失败。');
                      }
                    } catch (err: any) {
                      setError(err.message);
                      toast({
                        title: "错误",
                        description: err.message,
                        variant: "destructive",
                      });
                    } finally {
                      setIsLoading(false);
                    }
                  }}
                  disabled={isLoading || !selectedTable}
                  size="sm"
                >
                  显示全部 (100行)
                </Button>

              </div>
            </div>
          )}



          {/* 显示当前查询 */}
          {selectedTable && (
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <Label className="text-xs font-medium text-gray-600 mb-1 block">
                生成的查询语句:
              </Label>
              <code className="text-xs text-gray-800 font-mono">
                {buildStructuredQuery({ page: currentPage }) || `SELECT * FROM ${selectedTable} LIMIT ${pageSize} OFFSET 0;`}
              </code>
            </div>
          )}
        </CardContent>
      </Card>

      {error && (
        <Card className="mt-4 bg-destructive/10">
          <CardHeader>
            <CardTitle className="text-destructive">错误</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-destructive">{error}</p>
          </CardContent>
        </Card>
      )}

      {result && (
        <div className="mt-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold">查询结果</h2>
            {showPagination && totalRows > 0 && (
              <div className="text-sm text-gray-600">
                总计 {totalRows} 条记录，第 {currentPage} 页，共 {Math.ceil(totalRows / pageSize)} 页
              </div>
            )}
          </div>

          {Object.keys(result).length > 0 ? (
             Object.entries(result).map(([tableName, data]) => renderTable(tableName, data))
          ) : (
            <p>查询执行成功，但没有返回数据。</p>
          )}

          {/* 分页导航 */}
          {showPagination && totalRows > pageSize && (
            <div className="flex items-center justify-center gap-2 mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleStructuredQuery(currentPage - 1)}
                disabled={currentPage <= 1 || isLoading}
              >
                上一页
              </Button>

              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, Math.ceil(totalRows / pageSize)) }, (_, i) => {
                  const pageNum = Math.max(1, currentPage - 2) + i;
                  if (pageNum > Math.ceil(totalRows / pageSize)) return null;

                  return (
                    <Button
                      key={pageNum}
                      variant={pageNum === currentPage ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleStructuredQuery(pageNum)}
                      disabled={isLoading}
                    >
                      {pageNum}
                    </Button>
                  );
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handleStructuredQuery(currentPage + 1)}
                disabled={currentPage >= Math.ceil(totalRows / pageSize) || isLoading}
              >
                下一页
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}