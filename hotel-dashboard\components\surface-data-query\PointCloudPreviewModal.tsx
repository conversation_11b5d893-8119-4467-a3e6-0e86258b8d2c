"use client";

import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Loader2, <PERSON><PERSON><PERSON><PERSON>gle, ZoomIn, ZoomOut, RotateCcw } from 'lucide-react';
import { SurfaceFile } from '../../types/surface-data';
import { TransformWrapper, TransformComponent, ReactZoomPanPinchRef } from 'react-zoom-pan-pinch';

interface PointCloudPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  surfaceFile: SurfaceFile | null;
}

const PointCloudPreviewModal: React.FC<PointCloudPreviewModalProps> = ({
  isOpen,
  onClose,
  surfaceFile,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [previewImageSrc, setPreviewImageSrc] = useState<string | null>(null);
  const transformComponentRef = useRef<ReactZoomPanPinchRef | null>(null);

  useEffect(() => {
    if (isOpen && surfaceFile) {
      fetchPreviewData();
    } else {
      // Reset states when modal is closed or no file
      setPreviewImageSrc(null);
      setError(null);
      setIsLoading(false);
      // Also reset zoom on close
      if (transformComponentRef.current) {
        transformComponentRef.current.resetTransform();
      }
    }
  }, [isOpen, surfaceFile]);

  const fetchPreviewData = async () => {
    if (!surfaceFile) return;

    setIsLoading(true);
    setError(null);
    setPreviewImageSrc(null);
    if (transformComponentRef.current) {
      transformComponentRef.current.resetTransform();
    }

    try {
      const response = await fetch('/api/surface-data/preview', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ filePath: surfaceFile.fullPath }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData?.error || `Error fetching preview: ${response.statusText}`);
      }
      
      const responseData = await response.json();

      if (!responseData.success) {
        throw new Error(responseData.error || 'An unknown error occurred in the API.');
      }

      if (!responseData.imageData) {
        throw new Error('No image data received from API.');
      }
      setPreviewImageSrc(responseData.imageData);

    } catch (err) {
      console.error('Error fetching or processing preview data:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl h-[700px] flex flex-col">
        <DialogHeader>
          <DialogTitle>
            Point Cloud Preview: {surfaceFile?.name || 'Loading...'}
          </DialogTitle>
          <DialogClose asChild>
            <Button variant="ghost" size="icon" className="absolute top-4 right-4">
              &times;
            </Button>
          </DialogClose>
        </DialogHeader>
        <div className="flex-grow flex items-center justify-center relative border rounded-md overflow-hidden bg-slate-100">
          {isLoading && (
            <div className="absolute inset-0 flex flex-col items-center justify-center bg-background/80 z-20">
              <Loader2 className="h-12 w-12 animate-spin text-primary" />
              <p className="mt-2 text-muted-foreground">Loading preview data...</p>
            </div>
          )}
          {error && !isLoading && (
            <div className="absolute inset-0 flex flex-col items-center justify-center bg-destructive/10 p-4 z-20">
              <AlertTriangle className="h-12 w-12 text-destructive" />
              <p className="mt-2 text-destructive-foreground font-semibold">Error loading preview</p>
              <p className="mt-1 text-sm text-destructive-foreground/80 text-center">{error}</p>
              <Button onClick={fetchPreviewData} className="mt-4">Try Again</Button>
            </div>
          )}
          {!isLoading && !error && previewImageSrc && (
            <TransformWrapper
              ref={transformComponentRef}
              initialScale={1}
              minScale={0.5}
              maxScale={10}
              centerOnInit
              limitToBounds
              doubleClick={{ mode: 'reset' }}
            >
              {(utils: ReactZoomPanPinchRef) => (
                <>
                  <div className="absolute top-2 right-2 z-10 flex gap-1">
                    <Button variant="outline" size="icon" onClick={() => utils.zoomIn()}>
                      <ZoomIn className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon" onClick={() => utils.zoomOut()}>
                      <ZoomOut className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon" onClick={() => utils.resetTransform()}>
                      <RotateCcw className="h-4 w-4" />
                    </Button>
                  </div>
                  <TransformComponent
                    wrapperStyle={{ width: '100%', height: '100%' }}
                    contentStyle={{ width: '100%', height: '100%' }}
                  >
                    <img
                      src={previewImageSrc}
                      alt={`Preview of ${surfaceFile?.name || 'point cloud'}`}
                      className="w-full h-full object-contain"
                    />
                  </TransformComponent>
                </>
              )}
            </TransformWrapper>
          )}
           {!isLoading && !error && !previewImageSrc && (
            <div className="text-muted-foreground">No preview available or file not selected.</div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PointCloudPreviewModal;