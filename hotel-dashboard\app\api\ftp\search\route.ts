import { NextRequest, NextResponse } from 'next/server';
import { searchFiles as searchFtpFiles } from '../../../../lib/ftpClient';
import { FtpFile, SearchResult, SurfaceFile } from '../../../../types/surface-data';

const SEARCH_RESULT_LIMIT = 50; // 增加到50条

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const defaultBasePath = process.env.FTP_BASE_PATH_PRISM || '.';
  const basePath = searchParams.get('basePath') || defaultBasePath;
  const regexPattern = searchParams.get('regex');

  if (!regexPattern) {
    return NextResponse.json({ error: 'Missing regex query parameter.' }, { status: 400 });
  }

  try {
    // Consider security implications of basePath and regexPattern
    // Add validation/sanitization if necessary
    const ftpFiles: FtpFile[] = await searchFtpFiles(basePath, regexPattern, SEARCH_RESULT_LIMIT);

    const surfaceFiles: SurfaceFile[] = ftpFiles.map(ftpFile => {
      // Ensure basePath ends with a slash for consistent path manipulation
      const normalizedBasePath = basePath.endsWith('/') ? basePath : `${basePath}/`;
      // Calculate relative path
      let relativePath = ftpFile.path;
      if (ftpFile.path.startsWith(normalizedBasePath)) {
        relativePath = ftpFile.path.substring(normalizedBasePath.length);
      } else if (basePath === '.' && !ftpFile.path.startsWith('/')) {
        // If basePath is current directory, relativePath is same as ftpFile.path unless ftpFile.path is absolute
        relativePath = ftpFile.path;
      }
      // If ftpFile.path is absolute and basePath is relative, this logic might need adjustment
      // For now, assume ftpFile.path is always "deeper" or same level as basePath

      return {
        name: ftpFile.name,
        path: relativePath,
        fullPath: ftpFile.path,
        size: ftpFile.size,
        type: ftpFile.type,
        // modifyTime: ftpFile.date ? new Date(ftpFile.date).getTime() / 1000 : undefined, // Convert ISO string to Unix timestamp
        // For simplicity, if ftpFile.date is just a date string without time, this might not be accurate.
        // The FtpFile.date is an ISO string, so new Date() should parse it correctly.
        // Let's use the date string directly for now as modifyTime is optional or handle conversion if specifically required.
        // For now, let's keep it simple and not convert date to modifyTime unless explicitly needed by frontend.
        // modifyTime is optional in SurfaceFile, so we can omit it if not readily available or if conversion is complex.
      };
    });
    
    const result: SearchResult = {
      files: surfaceFiles,
      total: surfaceFiles.length, // Note: This is not the total matching files on FTP, just the count of returned limited results.
                           // A more accurate total would require a full scan without limit, which might be slow.
      limit: SEARCH_RESULT_LIMIT,
      offset: 0, // Assuming no pagination for now in this basic search
    };
    
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('FTP search API error:', error);
    return NextResponse.json({ error: 'Failed to search files on FTP server.', details: error.message }, { status: 500 });
  } finally {
    // In a real application, connection management needs to be more robust.
    // Closing after every request might be inefficient. Consider a pool or a longer-lived client.
    // For simplicity here, we close it.
    // However, if the client is shared across multiple requests, this might prematurely close it.
    // For now, let's assume ftpClient.ts handles its own connection state or we manage it per request.
    // closeFtpConnection(); // Potentially problematic if client is meant to be persistent.
    // Let's rely on the ftpClient's internal management or a higher-level strategy for now.
  }
}