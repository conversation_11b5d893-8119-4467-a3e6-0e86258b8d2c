"use client";

import React, { useState, useMemo, useRef, useEffect } from 'react';
import LogDisplayArea from '@/components/log-analysis/LogDisplayArea';
import LogChartView from '@/components/log-analysis/LogChartView';
import LogFileUpload from '@/components/log-analysis/LogFileUpload';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { exportElementAsImage } from '@/lib/exportUtils';
import { ProcessedBlock as WorkerProcessedBlock } from '@/workers/logParser.definitions';

interface ChartDataItem {
  name: string;
  value: number;
  type: string;
  block_id: string;
}

interface ProcessedBlock extends WorkerProcessedBlock {
  data: ChartDataItem[];
}

interface LogDisplayAreaProps {
  dataChunks: ProcessedBlock[];
  onSelectionChange: (selectedBlocks: ProcessedBlock[]) => void;
  onStartExport: (exportIds: string[]) => void;
}

export default function LogAnalysisPage() {
  const [dataChunks, setDataChunks] = useState<ProcessedBlock[]>([]);
  const [selectedBlocksForChart, setSelectedBlocksForChart] = useState<ProcessedBlock[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExportingMode, setIsExportingMode] = useState(false);
  const [blocksToRenderForExport, setBlocksToRenderForExport] = useState<ProcessedBlock[]>([]);
  const exportTargetContainerRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  const handleProcessingStart = () => {
    console.log('[LogAnalysisPage] handleProcessingStart - START');
    setIsLoading(true);
    setError(null);
    setSelectedBlocksForChart([]);
    console.log('[LogAnalysisPage] handleProcessingStart - END');
  };

  const handleDataProcessed = (workerData: WorkerProcessedBlock[]) => {
    console.log('[LogAnalysisPage] handleDataProcessed - START. Received data count:', workerData.length);
    const processedData: ProcessedBlock[] = workerData.map(block => ({
      ...block,
      data: [] // Initialize empty data array, can be populated later if needed
    }));
    setDataChunks(processedData);
    setIsLoading(false);
    toast({
      title: "数据已加载",
      description: `成功处理了 ${workerData.length} 个数据块。`,
    });
    console.log('[LogAnalysisPage] handleDataProcessed - END');
  };

  const handleError = (errorMessage: string) => {
    console.log('[LogAnalysisPage] handleError - START. Received errorMessage:', errorMessage);
    setError(errorMessage);
    setIsLoading(false);
    console.log('[LogAnalysisPage] handleError - END');
  };

  const handleBlockSelectionChanged = (selectedBlocks: ProcessedBlock[]) => {
    console.log('[LogAnalysisPage] handleBlockSelectionChanged - START. Received selectedBlocks count:', selectedBlocks.length);
    setSelectedBlocksForChart(selectedBlocks);
    console.log('[LogAnalysisPage] handleBlockSelectionChanged - END');
  };

  const handleBlockSelect = (blockId: string) => {
    console.log('[LogAnalysisPage] handleBlockSelect - START. Received blockId:', blockId);
    const block = dataChunks.find(b => b.block_id === blockId);
    if (block) {
      setSelectedBlocksForChart([block]);
    }
    console.log('[LogAnalysisPage] handleBlockSelect - END');
  };

  const chartDataForView = useMemo(() => {
    console.log('[LogAnalysisPage] Recalculating chartDataForView. Selected blocks count:', selectedBlocksForChart.length);
    return selectedBlocksForChart;
  }, [selectedBlocksForChart]);

  const initiateExportProcess = (exportIds: string[]) => {
    console.log('[LogAnalysisPage] initiateExportProcess - START. exportIds:', exportIds);
    const blocksToExport = dataChunks.filter(block => exportIds.includes(block.block_id));
    if (blocksToExport.length === 0) {
      toast({
        title: "导出错误",
        description: "没有找到要导出的数据块。",
        variant: "destructive",
      });
      return;
    }
    setBlocksToRenderForExport(blocksToExport);
    setIsExportingMode(true);
  };

  useEffect(() => {
    if (isExportingMode && blocksToRenderForExport.length > 0 && exportTargetContainerRef.current) {
      const allSelectedIds = blocksToRenderForExport.map(block => block.block_id);
      console.log('[LogAnalysisPage] Starting ZIP export for block IDs:', allSelectedIds);
      
      exportElementAsImage(
        exportTargetContainerRef.current,
        "exported_log_charts",
        allSelectedIds,
        (progress) => {
          console.log(`[LogAnalysisPage] Export progress: ${progress.toFixed(2)}%`);
        }
      ).then(() => {
        toast({
          title: "导出成功",
          description: "所有选中的图表已成功导出。",
        });
      }).catch((error) => {
        console.error('[LogAnalysisPage] Export failed:', error);
        toast({
          title: "导出失败",
          description: error instanceof Error ? error.message : "导出过程中发生错误。",
          variant: "destructive",
        });
      }).finally(() => {
        setIsExportingMode(false);
        setBlocksToRenderForExport([]);
      });
    }
  }, [isExportingMode, blocksToRenderForExport, dataChunks]);

  console.log(
    '[LogAnalysisPage] Rendering. isLoading:', isLoading,
    'error:', error,
    'dataChunks count:', dataChunks.length,
    'selectedBlocksForChart count:', selectedBlocksForChart.length,
    'chartDataForView count:', chartDataForView.length
  );

  return (
    <div className="flex flex-col h-full p-4 gap-4">
      <h1 className="text-2xl font-bold">日志分析</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="md:col-span-1">
          <LogFileUpload
            onProcessingStart={handleProcessingStart}
            onDataProcessed={handleDataProcessed}
            onError={handleError}
            disabled={isLoading}
          />
        </div>
        <div className="md:col-span-2">
          <LogDisplayArea
            dataChunks={dataChunks}
            onSelectionChange={handleBlockSelectionChanged}
            onStartExport={initiateExportProcess}
          />
        </div>
      </div>

      <div className="flex-grow mt-4">
        {isLoading && (
          <Card className="h-full flex items-center justify-center">
            <CardContent className="text-center">
              <p className="text-muted-foreground">正在处理文件...</p>
            </CardContent>
          </Card>
        )}
        {error && (
          <Card className="h-full flex items-center justify-center bg-destructive/10">
            <CardContent className="text-center">
              <p className="text-destructive font-semibold">发生错误</p>
              <p className="text-muted-foreground">{error}</p>
            </CardContent>
          </Card>
        )}
        {!isLoading && !error && (
          <div ref={exportTargetContainerRef} className="h-full">
            {isExportingMode ? (
              <LogChartView
                dataChunks={blocksToRenderForExport}
                selectedBlockIds={blocksToRenderForExport.map(b => b.block_id)}
                onBlockSelect={handleBlockSelect}
              />
            ) : chartDataForView.length > 0 ? (
              <LogChartView
                dataChunks={chartDataForView}
                selectedBlockIds={chartDataForView.map(b => b.block_id)}
                onBlockSelect={handleBlockSelect}
              />
            ) : (
              <Card className="h-full flex items-center justify-center">
                <CardContent className="text-center">
                  <p className="text-muted-foreground">
                    {dataChunks.length > 0
                      ? "请从左侧选择数据块以显示图表"
                      : "请先上传日志文件"}
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>
    </div>
  );
}